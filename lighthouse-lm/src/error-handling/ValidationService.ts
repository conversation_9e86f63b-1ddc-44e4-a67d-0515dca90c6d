import { Source } from '@/services/lighthouseService';
import { DiagramGenerationOptions } from '@/services/sourceDiagramService';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: string[];
}

export interface ValidationError {
  code: string;
  message: string;
  field?: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  code: string;
  message: string;
  field?: string;
  impact: 'performance' | 'quality' | 'usability';
}

export interface ContentComplexityMetrics {
  totalCharacters: number;
  totalWords: number;
  averageWordsPerSource: number;
  estimatedConcepts: number;
  estimatedRelationships: number;
  complexityScore: number;
  processingTimeEstimate: number; // in seconds
  memoryEstimate: number; // in MB
}

/**
 * Service for validating source content and diagram generation options
 * Provides early feedback to prevent failures during analysis
 */
export class ValidationService {
  private static readonly MAX_CONTENT_LENGTH = 1000000; // 1MB of text
  private static readonly MAX_SOURCES = 50;
  private static readonly MAX_CONCEPTS = 100;
  private static readonly MIN_CONTENT_LENGTH = 10;
  private static readonly COMPLEXITY_THRESHOLDS = {
    low: 0.3,
    medium: 0.6,
    high: 0.8
  };

  /**
   * Validate sources for diagram generation
   */
  static validateSources(sources: Source[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: string[] = [];

    // Check if sources array is empty
    if (!sources || sources.length === 0) {
      errors.push({
        code: 'NO_SOURCES',
        message: 'No sources selected for analysis',
        severity: 'error'
      });
      suggestions.push('Select at least one source to generate a diagram');
      return { isValid: false, errors, warnings, suggestions };
    }

    // Check maximum sources limit
    if (sources.length > this.MAX_SOURCES) {
      errors.push({
        code: 'TOO_MANY_SOURCES',
        message: `Too many sources selected (${sources.length}). Maximum allowed: ${this.MAX_SOURCES}`,
        severity: 'error'
      });
      suggestions.push(`Reduce the number of sources to ${this.MAX_SOURCES} or fewer`);
    }

    // Validate individual sources
    let totalContentLength = 0;
    let emptySourceCount = 0;
    let invalidSourceCount = 0;

    sources.forEach((source, index) => {
      const sourceErrors = this.validateSingleSource(source, index);
      errors.push(...sourceErrors.filter(e => e.severity === 'error'));
      warnings.push(...sourceErrors.filter(e => e.severity === 'warning').map(e => ({
        code: e.code,
        message: e.message,
        field: e.field,
        impact: 'quality' as const
      })));

      if (source.content) {
        totalContentLength += source.content.length;
      } else {
        emptySourceCount++;
      }

      if (!source.id || !source.title) {
        invalidSourceCount++;
      }
    });

    // Check total content length
    if (totalContentLength > this.MAX_CONTENT_LENGTH) {
      warnings.push({
        code: 'LARGE_CONTENT',
        message: `Total content size is large (${Math.round(totalContentLength / 1000)}KB). This may slow down processing.`,
        impact: 'performance'
      });
      suggestions.push('Consider splitting large sources or reducing content size');
    }

    // Check for empty sources
    if (emptySourceCount > 0) {
      warnings.push({
        code: 'EMPTY_SOURCES',
        message: `${emptySourceCount} source(s) have no content`,
        impact: 'quality'
      });
      suggestions.push('Remove empty sources or add content to improve analysis quality');
    }

    // Check for invalid sources
    if (invalidSourceCount > 0) {
      errors.push({
        code: 'INVALID_SOURCES',
        message: `${invalidSourceCount} source(s) are missing required fields`,
        severity: 'error'
      });
    }

    // Content diversity check
    const uniqueTitles = new Set(sources.map(s => s.title?.toLowerCase()));
    if (uniqueTitles.size < sources.length * 0.8) {
      warnings.push({
        code: 'LOW_DIVERSITY',
        message: 'Many sources have similar titles, which may reduce analysis quality',
        impact: 'quality'
      });
      suggestions.push('Ensure sources cover diverse topics for better diagram generation');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Validate diagram generation options
   */
  static validateGenerationOptions(options: DiagramGenerationOptions): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: string[] = [];

    // Validate max_concepts
    if (options.max_concepts <= 0) {
      errors.push({
        code: 'INVALID_MAX_CONCEPTS',
        message: 'Maximum concepts must be greater than 0',
        field: 'max_concepts',
        severity: 'error'
      });
    } else if (options.max_concepts > this.MAX_CONCEPTS) {
      warnings.push({
        code: 'HIGH_MAX_CONCEPTS',
        message: `High concept limit (${options.max_concepts}) may create complex diagrams`,
        field: 'max_concepts',
        impact: 'usability'
      });
      suggestions.push('Consider reducing max concepts for clearer diagrams');
    }

    // Validate relationship_depth
    if (options.relationship_depth < 1 || options.relationship_depth > 5) {
      warnings.push({
        code: 'UNUSUAL_RELATIONSHIP_DEPTH',
        message: `Relationship depth of ${options.relationship_depth} may produce unexpected results`,
        field: 'relationship_depth',
        impact: 'quality'
      });
      suggestions.push('Recommended relationship depth is between 1-3');
    }

    // Validate minimum_confidence
    if (options.minimum_confidence < 0 || options.minimum_confidence > 1) {
      errors.push({
        code: 'INVALID_CONFIDENCE',
        message: 'Minimum confidence must be between 0 and 1',
        field: 'minimum_confidence',
        severity: 'error'
      });
    } else if (options.minimum_confidence > 0.8) {
      warnings.push({
        code: 'HIGH_CONFIDENCE_THRESHOLD',
        message: 'High confidence threshold may result in very few concepts',
        field: 'minimum_confidence',
        impact: 'quality'
      });
      suggestions.push('Consider lowering confidence threshold to 0.3-0.6');
    }

    // Validate diagram type compatibility
    if (options.diagram_type === 'timeline' && options.max_concepts > 20) {
      warnings.push({
        code: 'TIMELINE_COMPLEXITY',
        message: 'Timeline diagrams work best with fewer concepts',
        impact: 'usability'
      });
      suggestions.push('Reduce max concepts to 20 or fewer for timeline diagrams');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Analyze content complexity and provide estimates
   */
  static analyzeContentComplexity(sources: Source[]): ContentComplexityMetrics {
    let totalCharacters = 0;
    let totalWords = 0;
    let estimatedConcepts = 0;
    let estimatedRelationships = 0;

    sources.forEach(source => {
      if (source.content) {
        const content = source.content;
        totalCharacters += content.length;
        
        const words = content.split(/\s+/).filter(word => word.length > 0);
        totalWords += words.length;
        
        // Estimate concepts based on content patterns
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const capitalizedWords = content.match(/\b[A-Z][a-z]+/g) || [];
        const technicalTerms = content.match(/\b[A-Z]{2,}|\b\w+(?:System|Service|API|Database)\b/gi) || [];
        
        estimatedConcepts += Math.min(
          Math.floor(sentences.length * 0.3) + capitalizedWords.length * 0.1 + technicalTerms.length,
          50 // Cap per source
        );
        
        // Estimate relationships based on connecting words
        const relationshipWords = content.match(/\b(?:depends|requires|leads|causes|enables|contains)\b/gi) || [];
        estimatedRelationships += relationshipWords.length * 0.7;
      }
    });

    const averageWordsPerSource = sources.length > 0 ? totalWords / sources.length : 0;
    
    // Calculate complexity score (0-1)
    const lengthFactor = Math.min(totalCharacters / this.MAX_CONTENT_LENGTH, 1);
    const conceptDensity = totalWords > 0 ? Math.min(estimatedConcepts / (totalWords / 100), 1) : 0;
    const relationshipDensity = estimatedConcepts > 0 ? Math.min(estimatedRelationships / estimatedConcepts, 1) : 0;
    
    const complexityScore = Math.min((lengthFactor * 0.4 + conceptDensity * 0.4 + relationshipDensity * 0.2), 1);
    
    // Estimate processing time (rough approximation)
    const baseTime = 2; // seconds
    const contentTimeMultiplier = totalCharacters / 10000; // 1 second per 10k chars
    const complexityTimeMultiplier = complexityScore * 5;
    const processingTimeEstimate = Math.max(baseTime + contentTimeMultiplier + complexityTimeMultiplier, 1);
    
    // Estimate memory usage (rough approximation)
    const baseMemory = 5; // MB
    const contentMemory = totalCharacters / 100000; // 1MB per 100k chars
    const conceptMemory = estimatedConcepts * 0.01; // 10KB per concept
    const memoryEstimate = baseMemory + contentMemory + conceptMemory;

    return {
      totalCharacters,
      totalWords,
      averageWordsPerSource,
      estimatedConcepts,
      estimatedRelationships,
      complexityScore,
      processingTimeEstimate: Math.round(processingTimeEstimate),
      memoryEstimate: Math.round(memoryEstimate * 10) / 10
    };
  }

  /**
   * Get complexity level description
   */
  static getComplexityLevel(complexityScore: number): {
    level: 'low' | 'medium' | 'high' | 'extreme';
    description: string;
    recommendations: string[];
  } {
    if (complexityScore < this.COMPLEXITY_THRESHOLDS.low) {
      return {
        level: 'low',
        description: 'Simple content that should process quickly',
        recommendations: [
          'Processing should complete in under 30 seconds',
          'Consider increasing max concepts for more detailed diagrams'
        ]
      };
    } else if (complexityScore < this.COMPLEXITY_THRESHOLDS.medium) {
      return {
        level: 'medium',
        description: 'Moderate complexity with good balance of detail',
        recommendations: [
          'Expected processing time: 30-60 seconds',
          'Current settings should work well'
        ]
      };
    } else if (complexityScore < this.COMPLEXITY_THRESHOLDS.high) {
      return {
        level: 'high',
        description: 'Complex content that may take longer to process',
        recommendations: [
          'Expected processing time: 1-3 minutes',
          'Consider reducing max concepts or relationship depth',
          'Monitor progress and be prepared to cancel if needed'
        ]
      };
    } else {
      return {
        level: 'extreme',
        description: 'Very complex content with high processing requirements',
        recommendations: [
          'Expected processing time: 3+ minutes',
          'Strongly recommend reducing content size or complexity',
          'Consider splitting into multiple smaller diagrams',
          'Increase minimum confidence threshold to reduce noise'
        ]
      };
    }
  }

  /**
   * Validate a single source
   */
  private static validateSingleSource(source: Source, index: number): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check required fields
    if (!source.id) {
      errors.push({
        code: 'MISSING_SOURCE_ID',
        message: `Source at index ${index} is missing ID`,
        field: `sources[${index}].id`,
        severity: 'error'
      });
    }

    if (!source.title || source.title.trim().length === 0) {
      errors.push({
        code: 'MISSING_SOURCE_TITLE',
        message: `Source at index ${index} is missing title`,
        field: `sources[${index}].title`,
        severity: 'warning'
      });
    }

    // Check content
    if (!source.content || source.content.trim().length === 0) {
      errors.push({
        code: 'EMPTY_SOURCE_CONTENT',
        message: `Source "${source.title || 'Untitled'}" has no content`,
        field: `sources[${index}].content`,
        severity: 'warning'
      });
    } else if (source.content.length < this.MIN_CONTENT_LENGTH) {
      errors.push({
        code: 'INSUFFICIENT_CONTENT',
        message: `Source "${source.title}" has very little content (${source.content.length} characters)`,
        field: `sources[${index}].content`,
        severity: 'warning'
      });
    }

    // Check for potentially problematic content
    if (source.content && source.content.includes('\0')) {
      errors.push({
        code: 'BINARY_CONTENT',
        message: `Source "${source.title}" appears to contain binary data`,
        field: `sources[${index}].content`,
        severity: 'error'
      });
    }

    return errors;
  }

  /**
   * Validate Mermaid diagram syntax
   */
  static validateMermaidSyntax(diagramContent: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: string[] = [];

    if (!diagramContent || diagramContent.trim().length === 0) {
      errors.push({
        code: 'EMPTY_DIAGRAM',
        message: 'Diagram content is empty',
        severity: 'error'
      });
      return { isValid: false, errors, warnings, suggestions };
    }

    // Check for valid Mermaid diagram type
    const diagramTypeMatch = diagramContent.match(/^\s*(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|timeline|mindmap|gitgraph)/m);
    if (!diagramTypeMatch) {
      errors.push({
        code: 'INVALID_DIAGRAM_TYPE',
        message: 'Diagram does not start with a valid Mermaid diagram type',
        severity: 'error'
      });
      suggestions.push('Ensure diagram starts with a valid type like "flowchart TD" or "graph LR"');
    }

    // Check for common syntax issues
    const lines = diagramContent.split('\n');
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.length === 0) return;

      // Check for unmatched brackets
      const openBrackets = (trimmedLine.match(/[\[\(]/g) || []).length;
      const closeBrackets = (trimmedLine.match(/[\]\)]/g) || []).length;
      if (openBrackets !== closeBrackets) {
        warnings.push({
          code: 'UNMATCHED_BRACKETS',
          message: `Line ${index + 1}: Unmatched brackets`,
          impact: 'quality'
        });
      }

      // Check for invalid characters in node IDs
      const nodeIdMatch = trimmedLine.match(/^([A-Za-z0-9_-]+)[\[\(]/);
      if (nodeIdMatch && !/^[A-Za-z0-9_-]+$/.test(nodeIdMatch[1])) {
        warnings.push({
          code: 'INVALID_NODE_ID',
          message: `Line ${index + 1}: Node ID "${nodeIdMatch[1]}" contains invalid characters`,
          impact: 'quality'
        });
      }
    });

    // Check diagram complexity
    const nodeCount = (diagramContent.match(/\w+[\[\(]/g) || []).length;
    const edgeCount = (diagramContent.match(/-->/g) || []).length;
    
    if (nodeCount > 50) {
      warnings.push({
        code: 'COMPLEX_DIAGRAM',
        message: `Diagram has many nodes (${nodeCount}), which may affect readability`,
        impact: 'usability'
      });
      suggestions.push('Consider simplifying the diagram or splitting into multiple diagrams');
    }

    if (edgeCount > 100) {
      warnings.push({
        code: 'MANY_CONNECTIONS',
        message: `Diagram has many connections (${edgeCount}), which may be hard to follow`,
        impact: 'usability'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }
}