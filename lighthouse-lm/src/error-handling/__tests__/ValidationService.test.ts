import { describe, it, expect } from 'vitest';
import { ValidationService } from '../ValidationService';
import { Source } from '../../../../services/lighthouseService';
import { DiagramGenerationOptions } from '../../../../services/sourceDiagramService';

describe('ValidationService', () => {
  const mockSource: Source = {
    id: 'test-source-1',
    title: 'Test Source',
    content: 'This is a test source with some content for analysis.',
    type: 'text',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const mockOptions: DiagramGenerationOptions = {
    diagram_type: 'flowchart',
    max_concepts: 20,
    relationship_depth: 2,
    include_metadata: true,
    filter_by_relevance: true,
    minimum_confidence: 0.3
  };

  describe('validateSources', () => {
    it('should validate valid sources successfully', () => {
      const result = ValidationService.validateSources([mockSource]);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty sources array', () => {
      const result = ValidationService.validateSources([]);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe('NO_SOURCES');
    });

    it('should reject too many sources', () => {
      const manySources = Array(60).fill(null).map((_, i) => ({
        ...mockSource,
        id: `source-${i}`
      }));
      
      const result = ValidationService.validateSources(manySources);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.code === 'TOO_MANY_SOURCES')).toBe(true);
    });

    it('should warn about empty source content', () => {
      const emptySource = { ...mockSource, content: '' };
      const result = ValidationService.validateSources([emptySource]);
      
      expect(result.warnings.some(w => w.code === 'EMPTY_SOURCES')).toBe(true);
    });

    it('should warn about large content size', () => {
      const largeContent = 'x'.repeat(1100000); // > 1MB
      const largeSource = { ...mockSource, content: largeContent };
      const result = ValidationService.validateSources([largeSource]);
      
      expect(result.warnings.some(w => w.code === 'LARGE_CONTENT')).toBe(true);
    });

    it('should detect low diversity in source titles', () => {
      const similarSources = [
        { ...mockSource, id: '1', title: 'Test Document' },
        { ...mockSource, id: '2', title: 'Test Document' },
        { ...mockSource, id: '3', title: 'Test Document' },
        { ...mockSource, id: '4', title: 'Test Document' },
        { ...mockSource, id: '5', title: 'Different Title' }
      ];
      
      const result = ValidationService.validateSources(similarSources);
      
      expect(result.warnings.some(w => w.code === 'LOW_DIVERSITY')).toBe(true);
    });
  });

  describe('validateGenerationOptions', () => {
    it('should validate valid options successfully', () => {
      const result = ValidationService.validateGenerationOptions(mockOptions);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid max_concepts', () => {
      const invalidOptions = { ...mockOptions, max_concepts: 0 };
      const result = ValidationService.validateGenerationOptions(invalidOptions);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.code === 'INVALID_MAX_CONCEPTS')).toBe(true);
    });

    it('should warn about high max_concepts', () => {
      const highOptions = { ...mockOptions, max_concepts: 150 };
      const result = ValidationService.validateGenerationOptions(highOptions);
      
      expect(result.warnings.some(w => w.code === 'HIGH_MAX_CONCEPTS')).toBe(true);
    });

    it('should reject invalid confidence range', () => {
      const invalidOptions = { ...mockOptions, minimum_confidence: 1.5 };
      const result = ValidationService.validateGenerationOptions(invalidOptions);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.code === 'INVALID_CONFIDENCE')).toBe(true);
    });

    it('should warn about unusual relationship depth', () => {
      const unusualOptions = { ...mockOptions, relationship_depth: 10 };
      const result = ValidationService.validateGenerationOptions(unusualOptions);
      
      expect(result.warnings.some(w => w.code === 'UNUSUAL_RELATIONSHIP_DEPTH')).toBe(true);
    });

    it('should warn about timeline complexity', () => {
      const timelineOptions = { 
        ...mockOptions, 
        diagram_type: 'timeline' as any,
        max_concepts: 50 
      };
      const result = ValidationService.validateGenerationOptions(timelineOptions);
      
      expect(result.warnings.some(w => w.code === 'TIMELINE_COMPLEXITY')).toBe(true);
    });
  });

  describe('analyzeContentComplexity', () => {
    it('should analyze simple content correctly', () => {
      const simpleSource = {
        ...mockSource,
        content: 'This is a simple test. It has basic content.'
      };
      
      const result = ValidationService.analyzeContentComplexity([simpleSource]);
      
      expect(result.totalCharacters).toBe(simpleSource.content.length);
      expect(result.totalWords).toBeGreaterThan(0);
      expect(result.complexityScore).toBeGreaterThanOrEqual(0);
      expect(result.complexityScore).toBeLessThanOrEqual(1);
      expect(result.processingTimeEstimate).toBeGreaterThan(0);
      expect(result.memoryEstimate).toBeGreaterThan(0);
    });

    it('should handle empty sources', () => {
      const result = ValidationService.analyzeContentComplexity([]);
      
      expect(result.totalCharacters).toBe(0);
      expect(result.totalWords).toBe(0);
      expect(result.estimatedConcepts).toBe(0);
      expect(result.estimatedRelationships).toBe(0);
    });

    it('should estimate concepts from content patterns', () => {
      const conceptRichSource = {
        ...mockSource,
        content: `
          The UserService depends on the DatabaseConnection.
          The AuthenticationSystem requires the UserService.
          The APIGateway contains multiple endpoints.
          John Smith manages the Development Team.
          Microsoft Corporation provides the cloud services.
        `
      };
      
      const result = ValidationService.analyzeContentComplexity([conceptRichSource]);
      
      expect(result.estimatedConcepts).toBeGreaterThan(5);
      expect(result.estimatedRelationships).toBeGreaterThan(2);
    });

    it('should calculate higher complexity for technical content', () => {
      const technicalSource = {
        ...mockSource,
        content: `
          The MicroserviceArchitecture depends on the ServiceMesh.
          The KubernetesCluster requires the ContainerRegistry.
          The DatabaseSystem contains UserTable and OrderTable.
          The APIGateway enables the AuthenticationService.
          The LoadBalancer causes traffic distribution.
        `
      };
      
      const simpleSource = {
        ...mockSource,
        content: 'This is simple text without technical terms.'
      };
      
      const technicalResult = ValidationService.analyzeContentComplexity([technicalSource]);
      const simpleResult = ValidationService.analyzeContentComplexity([simpleSource]);
      
      expect(technicalResult.complexityScore).toBeGreaterThan(simpleResult.complexityScore);
    });
  });

  describe('getComplexityLevel', () => {
    it('should return low complexity for simple content', () => {
      const result = ValidationService.getComplexityLevel(0.1);
      
      expect(result.level).toBe('low');
      expect(result.description).toContain('Simple');
      expect(result.recommendations).toHaveLength(2);
    });

    it('should return medium complexity for moderate content', () => {
      const result = ValidationService.getComplexityLevel(0.5);
      
      expect(result.level).toBe('medium');
      expect(result.description).toContain('Moderate');
    });

    it('should return high complexity for complex content', () => {
      const result = ValidationService.getComplexityLevel(0.7);
      
      expect(result.level).toBe('high');
      expect(result.description).toContain('Complex');
      expect(result.recommendations.length).toBeGreaterThan(2);
    });

    it('should return extreme complexity for very complex content', () => {
      const result = ValidationService.getComplexityLevel(0.9);
      
      expect(result.level).toBe('extreme');
      expect(result.description).toContain('Very complex');
      expect(result.recommendations.length).toBeGreaterThan(3);
    });
  });

  describe('validateMermaidSyntax', () => {
    it('should validate correct Mermaid syntax', () => {
      const validDiagram = `
        flowchart TD
          A[Start] --> B[Process]
          B --> C[End]
      `;
      
      const result = ValidationService.validateMermaidSyntax(validDiagram);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty diagram content', () => {
      const result = ValidationService.validateMermaidSyntax('');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.code === 'EMPTY_DIAGRAM')).toBe(true);
    });

    it('should reject invalid diagram type', () => {
      const invalidDiagram = 'invalid diagram content';
      const result = ValidationService.validateMermaidSyntax(invalidDiagram);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.code === 'INVALID_DIAGRAM_TYPE')).toBe(true);
    });

    it('should warn about complex diagrams', () => {
      const complexDiagram = `
        flowchart TD
        ${Array(60).fill(null).map((_, i) => `A${i}[Node${i}] --> A${i+1}[Node${i+1}]`).join('\n')}
      `;
      
      const result = ValidationService.validateMermaidSyntax(complexDiagram);
      
      expect(result.warnings.some(w => w.code === 'COMPLEX_DIAGRAM')).toBe(true);
    });

    it('should warn about unmatched brackets', () => {
      const invalidBrackets = `
        flowchart TD
          A[Start --> B[Process]
          B --> C[End
      `;
      
      const result = ValidationService.validateMermaidSyntax(invalidBrackets);
      
      expect(result.warnings.some(w => w.code === 'UNMATCHED_BRACKETS')).toBe(true);
    });
  });
});