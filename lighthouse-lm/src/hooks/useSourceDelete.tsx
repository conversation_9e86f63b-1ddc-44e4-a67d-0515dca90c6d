import { invoke } from '../lib/tauri-mock';
import { useMutationWithToast, mutationPresets } from './useMutationWithToast';

export const useSourceDelete = () => {
  const mutation = useMutationWithToast({
    mutationFn: async (sourceId: string) => {
      // Delete source from database
      const result = await invoke('delete_source', {
        sourceId,
      });
      return result;
    },
    ...mutationPresets.delete('Source'),
  });

  return {
    deleteSource: mutation.mutate,
    deleteSourceAsync: mutation.mutateAsync,
    isDeleting: mutation.isPending,
  };
};