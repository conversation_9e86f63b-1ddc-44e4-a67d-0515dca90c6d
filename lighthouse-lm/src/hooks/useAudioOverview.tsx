import { useState } from 'react';
import { invoke } from '../lib/tauri-mock';
import { useToast } from './useToast';

interface AudioOverviewResult {
  audioUrl: string;
  duration: number;
  transcript?: string;
}

export const useAudioOverview = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const { toast } = useToast();

  const generateAudioOverview = async (notebookId: string): Promise<AudioOverviewResult> => {
    try {
      setIsGenerating(true);
      
      
      const result = await invoke<AudioOverviewResult>('generate_audio_overview', {
        notebookId,
      });
      
      setAudioUrl(result.audioUrl);
      
      toast({
        title: "Audio Overview Generated",
        description: "The audio overview has been generated successfully.",
      });
      
      return result;
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate audio overview. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  const playAudio = (url: string) => {
    if (!url) return;
    
    const audio = new Audio(url);
    
    audio.addEventListener('play', () => {
      setIsPlaying(true);
    });
    
    audio.addEventListener('pause', () => {
      setIsPlaying(false);
    });
    
    audio.addEventListener('ended', () => {
      setIsPlaying(false);
    });
    
    audio.play().catch(error => {
      toast({
        title: "Playback Failed",
        description: "Failed to play the audio. Please try again.",
        variant: "destructive",
      });
    });
    
    return audio;
  };

  const stopAudio = () => {
    setIsPlaying(false);
  };

  const downloadAudio = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
      
      toast({
        title: "Download Started",
        description: "The audio file is being downloaded.",
      });
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download the audio file. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    generateAudioOverview,
    playAudio,
    stopAudio,
    downloadAudio,
    isGenerating,
    isPlaying,
    audioUrl,
  };
};