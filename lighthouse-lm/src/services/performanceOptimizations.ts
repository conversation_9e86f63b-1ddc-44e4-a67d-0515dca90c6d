/**
 * Performance optimization utilities for large source sets
 * Implements chunking, caching, lazy loading, and memory optimization
 */

import { Source } from '../services/lighthouseService';
import { SourceAnalysisResult, MultiSourceAnalysisResult } from '../types/sourceAnalysis';

// Configuration constants
export const PERFORMANCE_CONFIG = {
  // Chunking configuration
  MAX_CHUNK_SIZE: 10, // Maximum sources per chunk
  MIN_CHUNK_SIZE: 3,  // Minimum sources per chunk
  OPTIMAL_CHUNK_SIZE: 5, // Preferred chunk size for balanced processing
  
  // Caching configuration
  CACHE_TTL_MS: 30 * 60 * 1000, // 30 minutes cache TTL
  MAX_CACHE_SIZE: 100, // Maximum number of cached analyses
  CACHE_CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes cleanup interval
  
  // Memory optimization
  MAX_CONTENT_LENGTH: 50000, // Maximum content length to process at once
  CONTENT_CHUNK_SIZE: 10000, // Size of content chunks for processing
  GC_THRESHOLD: 50, // Trigger garbage collection after N operations
  
  // Lazy loading
  INITIAL_RENDER_COUNT: 5, // Initial number of diagram elements to render
  LAZY_LOAD_BATCH_SIZE: 10, // Number of elements to load per batch
  VIEWPORT_BUFFER: 2, // Number of off-screen elements to keep loaded
} as const;

// Cache interfaces
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}


// Chunking utilities
export class SourceChunker {
  /**
   * Split sources into optimal chunks for processing
   */
  static chunkSources(sources: Source[], options?: {
    maxChunkSize?: number;
    balanceBySize?: boolean;
    prioritySourceIds?: string[];
  }): Source[][] {
    const {
      maxChunkSize = PERFORMANCE_CONFIG.OPTIMAL_CHUNK_SIZE,
      balanceBySize = true,
      prioritySourceIds = []
    } = options || {};

    if (sources.length <= maxChunkSize) {
      return [sources];
    }

    // Sort sources by priority and size
    const sortedSources = this.sortSourcesForChunking(sources, prioritySourceIds, balanceBySize);
    
    const chunks: Source[][] = [];
    let currentChunk: Source[] = [];
    let currentChunkSize = 0;

    for (const source of sortedSources) {
      const sourceSize = this.estimateSourceSize(source);
      
      // Check if adding this source would exceed limits
      if (currentChunk.length >= maxChunkSize || 
          (balanceBySize && currentChunkSize + sourceSize > PERFORMANCE_CONFIG.MAX_CONTENT_LENGTH)) {
        
        if (currentChunk.length > 0) {
          chunks.push(currentChunk);
          currentChunk = [];
          currentChunkSize = 0;
        }
      }
      
      currentChunk.push(source);
      currentChunkSize += sourceSize;
    }

    // Add remaining sources
    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  /**
   * Create balanced chunks based on content size and complexity
   */
  static createBalancedChunks(sources: Source[], targetChunkCount?: number): Source[][] {
    if (!targetChunkCount) {
      targetChunkCount = Math.ceil(sources.length / PERFORMANCE_CONFIG.OPTIMAL_CHUNK_SIZE);
    }

    const totalSize = sources.reduce((sum, source) => sum + this.estimateSourceSize(source), 0);
    const targetChunkSize = totalSize / targetChunkCount;

    const chunks: Source[][] = [];
    let currentChunk: Source[] = [];
    let currentSize = 0;

    for (const source of sources) {
      const sourceSize = this.estimateSourceSize(source);
      
      if (currentSize + sourceSize > targetChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = [];
        currentSize = 0;
      }
      
      currentChunk.push(source);
      currentSize += sourceSize;
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  private static sortSourcesForChunking(
    sources: Source[], 
    priorityIds: string[], 
    balanceBySize: boolean
  ): Source[] {
    return [...sources].sort((a, b) => {
      // Priority sources first
      const aPriority = priorityIds.includes(a.id) ? 1 : 0;
      const bPriority = priorityIds.includes(b.id) ? 1 : 0;
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      // Then by size if balancing is enabled
      if (balanceBySize) {
        const aSize = this.estimateSourceSize(a);
        const bSize = this.estimateSourceSize(b);
        return aSize - bSize; // Smaller sources first for better packing
      }
      
      return 0;
    });
  }

  private static estimateSourceSize(source: Source): number {
    const contentLength = source.content?.length || 0;
    const titleLength = source.title?.length || 0;
    return contentLength + titleLength;
  }
}

// Caching system
interface CacheStructure {
  singleSource: Map<string, CacheEntry<SourceAnalysisResult>>;
  multiSource: Map<string, CacheEntry<MultiSourceAnalysisResult>>;
  contentChunks: Map<string, CacheEntry<any>>;
}

export class AnalysisCache {
  private static instance: AnalysisCache;
  private cache: CacheStructure;
  private cleanupInterval: NodeJS.Timeout;
  private operationCount = 0;

  private constructor() {
    this.cache = {
      singleSource: new Map(),
      multiSource: new Map(),
      contentChunks: new Map()
    };

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, PERFORMANCE_CONFIG.CACHE_CLEANUP_INTERVAL);
  }

  static getInstance(): AnalysisCache {
    if (!AnalysisCache.instance) {
      AnalysisCache.instance = new AnalysisCache();
    }
    return AnalysisCache.instance;
  }

  /**
   * Get cached single source analysis
   */
  getSingleSourceAnalysis(sourceId: string): SourceAnalysisResult | null {
    const entry = this.cache.singleSource.get(sourceId);
    
    if (!entry) return null;
    
    // Check if cache entry is still valid
    if (Date.now() - entry.timestamp > PERFORMANCE_CONFIG.CACHE_TTL_MS) {
      this.cache.singleSource.delete(sourceId);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.data;
  }

  /**
   * Cache single source analysis
   */
  setSingleSourceAnalysis(sourceId: string, analysis: SourceAnalysisResult): void {
    const entry: CacheEntry<SourceAnalysisResult> = {
      data: analysis,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    };
    
    this.cache.singleSource.set(sourceId, entry);
    this.triggerCleanupIfNeeded();
  }

  /**
   * Get cached multi-source analysis
   */
  getMultiSourceAnalysis(sourceIds: string[]): MultiSourceAnalysisResult | null {
    const key = this.createMultiSourceKey(sourceIds);
    const entry = this.cache.multiSource.get(key);
    
    if (!entry) return null;
    
    // Check if cache entry is still valid
    if (Date.now() - entry.timestamp > PERFORMANCE_CONFIG.CACHE_TTL_MS) {
      this.cache.multiSource.delete(key);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.data;
  }

  /**
   * Cache multi-source analysis
   */
  setMultiSourceAnalysis(sourceIds: string[], analysis: MultiSourceAnalysisResult): void {
    const key = this.createMultiSourceKey(sourceIds);
    const entry: CacheEntry<MultiSourceAnalysisResult> = {
      data: analysis,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    };
    
    this.cache.multiSource.set(key, entry);
    this.triggerCleanupIfNeeded();
  }

  /**
   * Get cached content chunk analysis
   */
  getContentChunkAnalysis(contentHash: string): any | null {
    const entry = this.cache.contentChunks.get(contentHash);
    
    if (!entry) return null;
    
    // Check if cache entry is still valid
    if (Date.now() - entry.timestamp > PERFORMANCE_CONFIG.CACHE_TTL_MS) {
      this.cache.contentChunks.delete(contentHash);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.data;
  }

  /**
   * Cache content chunk analysis
   */
  setContentChunkAnalysis(contentHash: string, analysis: any): void {
    const entry: CacheEntry<any> = {
      data: analysis,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    };
    
    this.cache.contentChunks.set(contentHash, entry);
    this.triggerCleanupIfNeeded();
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    this.cache.singleSource.clear();
    this.cache.multiSource.clear();
    this.cache.contentChunks.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    singleSource: { size: number; hitRate: number };
    multiSource: { size: number; hitRate: number };
    contentChunks: { size: number; hitRate: number };
    totalMemoryUsage: number;
  } {
    return {
      singleSource: {
        size: this.cache.singleSource.size,
        hitRate: this.calculateHitRate(this.cache.singleSource)
      },
      multiSource: {
        size: this.cache.multiSource.size,
        hitRate: this.calculateHitRate(this.cache.multiSource)
      },
      contentChunks: {
        size: this.cache.contentChunks.size,
        hitRate: this.calculateHitRate(this.cache.contentChunks)
      },
      totalMemoryUsage: this.estimateMemoryUsage()
    };
  }

  private createMultiSourceKey(sourceIds: string[]): string {
    return [...sourceIds].sort().join('|');
  }

  private triggerCleanupIfNeeded(): void {
    this.operationCount++;
    
    if (this.operationCount >= PERFORMANCE_CONFIG.GC_THRESHOLD) {
      this.cleanup();
      this.operationCount = 0;
      
      // Suggest garbage collection
      if (global.gc) {
        global.gc();
      }
    }
  }

  private cleanup(): void {
    const now = Date.now();
    
    // Clean up expired entries
    this.cleanupMap(this.cache.singleSource, now);
    this.cleanupMap(this.cache.multiSource, now);
    this.cleanupMap(this.cache.contentChunks, now);
    
    // Clean up least recently used entries if cache is too large
    this.evictLRU(this.cache.singleSource, PERFORMANCE_CONFIG.MAX_CACHE_SIZE);
    this.evictLRU(this.cache.multiSource, PERFORMANCE_CONFIG.MAX_CACHE_SIZE);
    this.evictLRU(this.cache.contentChunks, PERFORMANCE_CONFIG.MAX_CACHE_SIZE);
  }

  private cleanupMap<T>(map: Map<string, CacheEntry<T>>, now: number): void {
    for (const [key, entry] of map.entries()) {
      if (now - entry.timestamp > PERFORMANCE_CONFIG.CACHE_TTL_MS) {
        map.delete(key);
      }
    }
  }

  private evictLRU<T>(map: Map<string, CacheEntry<T>>, maxSize: number): void {
    if (map.size <= maxSize) return;
    
    // Sort by last accessed time and remove oldest entries
    const entries = Array.from(map.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    const toRemove = entries.slice(0, map.size - maxSize);
    toRemove.forEach(([key]) => map.delete(key));
  }

  private calculateHitRate<T>(map: Map<string, CacheEntry<T>>): number {
    if (map.size === 0) return 0;
    
    const totalAccesses = Array.from(map.values())
      .reduce((sum, entry) => sum + entry.accessCount, 0);
    
    return totalAccesses / map.size;
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    let totalSize = 0;
    
    for (const entry of this.cache.singleSource.values()) {
      totalSize += JSON.stringify(entry.data).length * 2; // Rough UTF-16 estimation
    }
    
    for (const entry of this.cache.multiSource.values()) {
      totalSize += JSON.stringify(entry.data).length * 2;
    }
    
    for (const entry of this.cache.contentChunks.values()) {
      totalSize += JSON.stringify(entry.data).length * 2;
    }
    
    return totalSize;
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clearAll();
  }
}

// Memory optimization utilities
export class MemoryOptimizer {
  /**
   * Process content in chunks to reduce memory usage
   */
  static async processContentInChunks<T>(
    content: string,
    processor: (chunk: string, index: number) => Promise<T>,
    options?: {
      chunkSize?: number;
      overlap?: number;
      onProgress?: (progress: number) => void;
    }
  ): Promise<T[]> {
    const {
      chunkSize = PERFORMANCE_CONFIG.CONTENT_CHUNK_SIZE,
      overlap = 100,
      onProgress
    } = options || {};

    if (content.length <= chunkSize) {
      const result = await processor(content, 0);
      onProgress?.(1);
      return [result];
    }

    const results: T[] = [];
    const totalChunks = Math.ceil(content.length / (chunkSize - overlap));
    
    for (let i = 0; i < content.length; i += chunkSize - overlap) {
      const end = Math.min(i + chunkSize, content.length);
      const chunk = content.substring(i, end);
      
      const result = await processor(chunk, Math.floor(i / (chunkSize - overlap)));
      results.push(result);
      
      // Report progress
      const progress = (i + chunkSize) / content.length;
      onProgress?.(Math.min(progress, 1));
      
      // Allow event loop to process other tasks
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return results;
  }

  /**
   * Create a hash for content to enable caching
   */
  static createContentHash(content: string): string {
    // Simple hash function for content identification
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Optimize source content for processing
   */
  static optimizeSourceContent(source: Source): Source {
    if (!source.content) return source;
    
    // Truncate extremely long content
    let optimizedContent = source.content;
    if (optimizedContent.length > PERFORMANCE_CONFIG.MAX_CONTENT_LENGTH) {
      optimizedContent = optimizedContent.substring(0, PERFORMANCE_CONFIG.MAX_CONTENT_LENGTH) + '...';
    }
    
    // Remove excessive whitespace
    optimizedContent = optimizedContent.replace(/\s+/g, ' ').trim();
    
    return {
      ...source,
      content: optimizedContent
    };
  }

  /**
   * Batch process sources with memory management
   */
  static async batchProcessSources<T>(
    sources: Source[],
    processor: (source: Source) => Promise<T>,
    options?: {
      batchSize?: number;
      onProgress?: (progress: number) => void;
      onBatchComplete?: (batchIndex: number, results: T[]) => void;
    }
  ): Promise<T[]> {
    const {
      batchSize = PERFORMANCE_CONFIG.OPTIMAL_CHUNK_SIZE,
      onProgress,
      onBatchComplete
    } = options || {};

    const results: T[] = [];
    const totalBatches = Math.ceil(sources.length / batchSize);
    
    for (let i = 0; i < sources.length; i += batchSize) {
      const batch = sources.slice(i, i + batchSize);
      const batchIndex = Math.floor(i / batchSize);
      
      // Process batch in parallel
      const batchResults = await Promise.all(
        batch.map(source => processor(source))
      );
      
      results.push(...batchResults);
      
      // Report progress
      const progress = (i + batchSize) / sources.length;
      onProgress?.(Math.min(progress, 1));
      
      // Notify batch completion
      onBatchComplete?.(batchIndex, batchResults);
      
      // Allow garbage collection between batches
      if (batchIndex < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, 10));
        if (global.gc) {
          global.gc();
        }
      }
    }

    return results;
  }
}

// Lazy loading utilities
export interface LazyLoadState<T> {
  items: T[];
  loadedCount: number;
  isLoading: boolean;
  hasMore: boolean;
}

export class LazyLoader<T> {
  private items: T[];
  private batchSize: number;
  private loadedCount: number;

  constructor(items: T[], batchSize: number = PERFORMANCE_CONFIG.LAZY_LOAD_BATCH_SIZE) {
    this.items = items;
    this.batchSize = batchSize;
    this.loadedCount = 0;
  }

  /**
   * Get initial batch of items
   */
  getInitialBatch(): LazyLoadState<T> {
    const initialCount = Math.min(PERFORMANCE_CONFIG.INITIAL_RENDER_COUNT, this.items.length);
    this.loadedCount = initialCount;
    
    return {
      items: this.items.slice(0, initialCount),
      loadedCount: this.loadedCount,
      isLoading: false,
      hasMore: this.loadedCount < this.items.length
    };
  }

  /**
   * Load next batch of items
   */
  loadNextBatch(): LazyLoadState<T> {
    const nextBatchSize = Math.min(this.batchSize, this.items.length - this.loadedCount);
    const newLoadedCount = this.loadedCount + nextBatchSize;
    
    this.loadedCount = newLoadedCount;
    
    return {
      items: this.items.slice(0, newLoadedCount),
      loadedCount: this.loadedCount,
      isLoading: false,
      hasMore: this.loadedCount < this.items.length
    };
  }

  /**
   * Check if more items can be loaded
   */
  hasMore(): boolean {
    return this.loadedCount < this.items.length;
  }

  /**
   * Get total item count
   */
  getTotalCount(): number {
    return this.items.length;
  }

  /**
   * Reset loader state
   */
  reset(): void {
    this.loadedCount = 0;
  }
}

// Utility functions
export const PerformanceUtils = {
  /**
   * Debounce function for performance-sensitive operations
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  /**
   * Throttle function for performance-sensitive operations
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Measure execution time of async operations
   */
  async measureTime<T>(
    operation: () => Promise<T>,
    label?: string
  ): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await operation();
    const duration = performance.now() - start;
    
    if (label) {
    }
    
    return { result, duration };
  }
};