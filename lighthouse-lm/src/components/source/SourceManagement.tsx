import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '../ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { useToast } from '../../hooks/useToast';
import { useSources } from '../../hooks';
import { useFileUpload } from '../../hooks/useFileUpload';
import { useDropzone } from 'react-dropzone';
import { errorHandler } from '../../services/errorHandler';

// Import all the extracted components
import SidebarNavigation from './SidebarNavigation';
import SourceCard from './SourceCard';
import SourceListItem from './SourceListItem';
import SourceToolbar from './SourceToolbar';
import AdvancedFilters from './AdvancedFilters';
import FileUploadZone from './FileUploadZone';
import SourceTemplateGrid from './SourceTemplateGrid';
import OverviewStats from './OverviewStats';

// Import the advanced components
import SourceRelationshipView from './SourceRelationshipView';
import SourceComparisonView from './SourceComparisonView';
import SourceAnalyticsView from './SourceAnalyticsView';
import AIAssistantPanel from './AIAssistantPanel';
import SourceExportDialog from './SourceExportDialog';
import IntegrationsPanel from './IntegrationsPanel';
import CollaborationPanel from './CollaborationPanel';
import SourceVersionHistory from './SourceVersionHistory';
import SourceSettingsPanel from './SourceSettingsPanel';

// Import types
import {
  SourceManagementDialogProps,
  ViewMode,
  FilterType,
  SortBy,
  DateRange,
  SourceTemplate,
} from './types';
import { Source } from '../../types/chat';

// Import analytics service
import { analyticsService } from '../../services/analyticsService';

const SourceManagementDialog: React.FC<SourceManagementDialogProps> = ({
  open,
  onOpenChange,
  notebookId,
  onSourceSelect,
  onBulkOperation,
}) => {
  const { toast } = useToast();
  const { sources, isLoading: loading, error, deleteSource, updateSource } = useSources(notebookId);
  const { uploadMultipleFiles: uploadFiles, isUploading, uploadProgress } = useFileUpload();

  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined });
  const [tags, setTags] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<SourceTemplate | null>(null);
  const [sourceTemplates, setSourceTemplates] = useState<SourceTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  // Default templates - in real implementation this would come from API
  const defaultTemplates: SourceTemplate[] = [
    {
      id: '1',
      name: 'Research Paper',
      description: 'Academic papers and research documents',
      icon: null,
      category: 'Document',
      fields: [
        { name: 'title', type: 'text', required: true, placeholder: 'Paper title' },
        { name: 'authors', type: 'text', required: true, placeholder: 'Author names' },
        { name: 'abstract', type: 'textarea', required: false, placeholder: 'Abstract' },
        { name: 'keywords', type: 'tags', required: false, placeholder: 'Keywords' },
      ],
    },
    {
      id: '2',
      name: 'Code Repository',
      description: 'Source code and development projects',
      icon: null,
      category: 'Code',
      fields: [
        { name: 'repository', type: 'text', required: true, placeholder: 'Repository name' },
        { name: 'language', type: 'select', required: true, placeholder: 'Programming language' },
        { name: 'description', type: 'textarea', required: false, placeholder: 'Project description' },
      ],
    },
  ];

  // Track when source management is opened
  useEffect(() => {
    if (open) {
      analyticsService.trackComponentInteraction('source_management', 'open', {
        notebookId
      });
    }
  }, [open, notebookId]);

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleFileDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
      'application/json': ['.json'],
    },
    multiple: true,
  });

  // Handlers
  async function handleFileDrop(files: File[]) {
    try {
      // Convert File[] to FileList-like object
      const fileList = {
        ...files,
        item: (index: number) => files[index] || null,
        length: files.length
      } as FileList;
      
      await uploadFiles(fileList);
      toast({
        title: 'Upload successful',
        description: `${files.length} file(s) uploaded successfully.`,
      });
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload files. Please try again.',
        variant: 'destructive',
      });
    }
  }

  const handleSourceSelect = useCallback((sourceId: string) => {
    setSelectedSources(prev =>
      prev.includes(sourceId)
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    );
  }, []);

  const handleBulkOperation = useCallback(async (operation: string) => {
    if (selectedSources.length === 0) return;

    try {
      switch (operation) {
        case 'delete':
          await Promise.all(selectedSources.map(id => deleteSource(id)));
          toast({
            title: 'Sources deleted',
            description: `${selectedSources.length} source(s) deleted successfully.`,
          });
          break;
        case 'archive':
          // Archive logic would go here
          toast({
            title: 'Sources archived',
            description: `${selectedSources.length} source(s) archived successfully.`,
          });
          break;
        case 'tag':
          // Tag logic would go here
          toast({
            title: 'Sources tagged',
            description: `${selectedSources.length} source(s) tagged successfully.`,
          });
          break;
      }
      
      setSelectedSources([]);
      onBulkOperation?.(operation, selectedSources);
    } catch (error) {
      toast({
        title: 'Operation failed',
        description: 'Failed to perform bulk operation. Please try again.',
        variant: 'destructive',
      });
    }
  }, [selectedSources, deleteSource, onBulkOperation, toast]);

  const handleVersionHistory = useCallback((source: Source) => {
    // Version history logic would go here
    toast({
      title: 'Version History',
      description: `Viewing version history for ${source.title}`,
    });
  }, [toast]);

  const handleTemplateSelect = useCallback((template: SourceTemplate) => {
    setSelectedTemplate(template);
    toast({
      title: 'Template selected',
      description: `Selected template: ${template.name}`,
    });
  }, [toast]);

  // Filtered and sorted sources
  const filteredSources = useMemo(() => {
    let filtered = sources;

    // Apply search filter
     if (searchQuery) {
       filtered = filtered.filter(source =>
         source.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
         (source.content && source.content.toLowerCase().includes(searchQuery.toLowerCase()))
       );
     }
 
     // Apply type filter
     if (filterType !== 'all') {
       filtered = filtered.filter(source => (source as any).type === filterType);
     }

    // Apply date range filter
    if (dateRange.from || dateRange.to) {
      filtered = filtered.filter(source => {
        const sourceDate = source.created_at ? new Date(source.created_at) : new Date();
        if (dateRange.from && sourceDate < dateRange.from) return false;
        if (dateRange.to && sourceDate > dateRange.to) return false;
        return true;
      });
    }

    // Apply tags filter
     if (tags.length > 0) {
       filtered = filtered.filter(source =>
         (source as any).tags?.some((tag: string) => tags.includes(tag))
       );
     }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'size':
          return ((b.content?.length || 0) - (a.content?.length || 0));
        case 'type':
           return ((a as any).type || '').localeCompare((b as any).type || '');
        case 'date':
        default:
          return (b.created_at ? new Date(b.created_at).getTime() : 0) - (a.created_at ? new Date(a.created_at).getTime() : 0);
      }
    });

    return filtered;
  }, [sources, searchQuery, filterType, sortBy, dateRange, tags]);

  const handleSelectAll = useCallback(() => {
    const filtered = filteredSources;
    if (selectedSources.length === filtered.length) {
      setSelectedSources([]);
    } else {
      setSelectedSources(filtered.map(source => source.id));
    }
  }, [selectedSources.length, filteredSources]);

  // Calculate stats
  const stats = useMemo(() => {
    const totalSources = sources.length;
    const recentUploads = sources.filter(source => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return source.created_at ? new Date(source.created_at) > weekAgo : false;
    }).length;
    
    const totalSize = sources.reduce((sum, source) => sum + (source.content?.length || 0), 0);
    const storageUsed = totalSize > 1024 * 1024 
      ? `${(totalSize / (1024 * 1024)).toFixed(1)}M chars`
      : `${(totalSize / 1024).toFixed(1)}K chars`;
    
    const collaborators = 5; // Mock data

    return { totalSources, recentUploads, storageUsed, collaborators };
  }, [sources]);

  // Sources will be loaded automatically by the hook

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl h-[90vh] p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>Source Management</DialogTitle>
          <DialogDescription>
            Manage, organize, and analyze your knowledge sources
          </DialogDescription>
        </DialogHeader>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 border-r bg-muted/30">
            <SidebarNavigation
              activeTab={activeTab}
              onTabChange={setActiveTab}
              totalSources={stats.totalSources}
              selectedCount={selectedSources.length}
              storageUsed={stats.storageUsed}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            <ScrollArea className="flex-1">
              <div className="p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  {/* Overview Tab */}
                  <TabsContent value="overview" className="mt-0">
                    <OverviewStats
                      totalSources={stats.totalSources}
                      recentUploads={stats.recentUploads}
                      storageUsed={stats.storageUsed}
                      collaborators={stats.collaborators}
                    />
                  </TabsContent>

                  {/* Sources Tab */}
                  <TabsContent value="sources" className="mt-0">
                    <div className="space-y-4">
                      <SourceToolbar
                        searchQuery={searchQuery}
                        onSearchChange={setSearchQuery}
                        filterType={filterType}
                        onFilterChange={setFilterType}
                        sortBy={sortBy}
                        onSortChange={setSortBy}
                        viewMode={viewMode}
                        onViewModeChange={setViewMode}
                        selectedCount={selectedSources.length}
                        totalCount={filteredSources.length}
                        onSelectAll={handleSelectAll}
                        onBulkOperation={handleBulkOperation}
                        showAdvancedFilters={showAdvancedFilters}
                        onToggleAdvancedFilters={() => setShowAdvancedFilters(!showAdvancedFilters)}
                      />

                      {showAdvancedFilters && (
                        <AdvancedFilters
                          dateRange={dateRange}
                          onDateRangeChange={setDateRange}
                          tags={tags}
                          onTagsChange={setTags}
                        />
                      )}

                      {/* Sources Display */}
                      {loading ? (
                        <div className="text-center py-8">Loading sources...</div>
                      ) : error ? (
                        <div className="text-center py-8 text-destructive">Error loading sources</div>
                      ) : filteredSources.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          No sources found matching your criteria
                        </div>
                      ) : (
                        <div className={viewMode === 'grid' 
                          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                          : 'space-y-2'
                        }>
                          {filteredSources.map((source) => (
                             viewMode === 'grid' ? (
                               <SourceCard
                                 key={source.id}
                                 source={source as any}
                                 isSelected={selectedSources.includes(source.id)}
                                 onSelect={handleSourceSelect}
                                 onVersionHistory={handleVersionHistory}
                               />
                             ) : (
                               <SourceListItem
                                 key={source.id}
                                 source={source as any}
                                 isSelected={selectedSources.includes(source.id)}
                                 onSelect={handleSourceSelect}
                               />
                             )
                           ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Upload Tab */}
                  <TabsContent value="upload" className="mt-0">
                    <FileUploadZone
                      onDrop={handleFileDrop}
                      isUploading={isUploading}
                      uploadProgress={uploadProgress}
                      isDragActive={isDragActive}
                      getRootProps={getRootProps}
                      getInputProps={getInputProps}
                    />
                  </TabsContent>

                  {/* Templates Tab */}
                  <TabsContent value="templates" className="mt-0">
                    <SourceTemplateGrid
                      templates={sourceTemplates}
                      onTemplateSelect={handleTemplateSelect}
                    />
                  </TabsContent>

                  {/* Advanced Components Tabs */}
                  <TabsContent value="relationships" className="mt-0">
                    <SourceRelationshipView sources={filteredSources.map(s => ({
                      id: s.id,
                      title: s.title,
                      content: s.content || '',
                      type: s.source_type || 'document',
                      created_at: s.created_at,
                      size: s.file_size,
                      tags: [],
                      status: s.processing_status,
                      file_path: s.file_path,
                      file_size: s.file_size
                    }))} 
                    relations={[]}
                    onCreateRelation={(relation) => console.log('Create relation:', relation)}
                    onDeleteRelation={(id) => console.log('Delete relation:', id)}
                    />
                  </TabsContent>

                  <TabsContent value="comparison" className="mt-0">
                    <SourceComparisonView sources={filteredSources.map(s => ({
                      id: s.id,
                      title: s.title,
                      content: s.content || '',
                      type: s.source_type || 'document',
                      created_at: s.created_at,
                      size: s.file_size,
                      tags: [],
                      status: s.processing_status,
                      file_path: s.file_path,
                      file_size: s.file_size
                    }))} />
                  </TabsContent>

                  <TabsContent value="analytics" className="mt-0">
                    <SourceAnalyticsView sources={filteredSources.map(s => ({
                      id: s.id,
                      title: s.title,
                      content: s.content || '',
                      type: s.source_type || 'document',
                      created_at: s.created_at,
                      size: s.file_size,
                      tags: [],
                      status: s.processing_status,
                      file_path: s.file_path,
                      file_size: s.file_size
                    }))} />
                  </TabsContent>

                  <TabsContent value="ai-assistant" className="mt-0">
                    <AIAssistantPanel sources={filteredSources.map(s => ({
                      id: s.id,
                      title: s.title,
                      content: s.content || '',
                      type: s.source_type || 'document',
                      created_at: s.created_at,
                      size: s.file_size,
                      tags: [],
                      status: s.processing_status,
                      file_path: s.file_path,
                      file_size: s.file_size
                    }))} />
                  </TabsContent>

                  <TabsContent value="export" className="mt-0">
                    <SourceExportDialog 
                      open={false}
                      onOpenChange={() => {}} 
                      sources={(selectedSources.length > 0 ? filteredSources.filter(s => selectedSources.includes(s.id)) : filteredSources).map(s => ({
                        id: s.id,
                        title: s.title,
                        content: s.content || '',
                        type: s.source_type || 'document',
                        created_at: s.created_at,
                        size: s.content?.length || 0,
                        tags: [],
                        status: s.processing_status,
                        file_path: s.file_path,
                        file_size: s.content?.length || 0
                      }))}
                    />
                  </TabsContent>

                  <TabsContent value="integrations" className="mt-0">
                    <IntegrationsPanel 
                      open={true}
                      onOpenChange={() => {}}
                    />
                  </TabsContent>

                  <TabsContent value="collaboration" className="mt-0">
                    <CollaborationPanel 
                      open={false}
                      onOpenChange={() => {}}
                      selectedSources={filteredSources.map(s => ({
                        id: s.id,
                        title: s.title,
                        content: s.content || '',
                        type: s.source_type || 'document',
                        created_at: s.created_at,
                        size: s.content?.length || 0,
                        tags: [],
                        status: s.processing_status,
                        file_path: s.file_path,
                        file_size: s.content?.length || 0
                      }))}
                    />
                  </TabsContent>

                  <TabsContent value="settings" className="mt-0">
                    {filteredSources.length > 0 && (
                      <SourceSettingsPanel 
                        open={false}
                        onOpenChange={() => {}}
                        source={{
                          id: filteredSources[0].id,
                          title: filteredSources[0].title,
                          content: filteredSources[0].content || '',
                          type: filteredSources[0].source_type || 'document',
                          created_at: filteredSources[0].created_at,
                          tags: [],
                          processing_status: filteredSources[0].processing_status,
                          file_path: filteredSources[0].file_path,
                          file_size: filteredSources[0].file_size
                        }}
                        onSaveSettings={async (settings) => { console.log('Save settings:', settings); }}
                      />
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceManagementDialog;