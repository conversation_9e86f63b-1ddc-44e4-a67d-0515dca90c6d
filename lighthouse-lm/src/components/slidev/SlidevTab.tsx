import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '../ui/button';
import { Plus, Bot, Upload, RefreshCw } from 'lucide-react';
import { Card } from '../ui/card';
import { ScrollArea } from '../ui/scroll-area';
import { invoke, open, readTextFile } from '../../lib/tauri-mock';
// EmptyState component will be created inline
import { useToast } from '../../hooks/useToast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../ui/alert-dialog';
import { useChatMessages } from '../../hooks';

// Import our extracted components and types
import { 
  Slide, 
  Presentation, 
  PresentationSummary, 
  GenerateSlidesRequest, 
  GenerateSlidesResponse, 
  ViewMode, 
  GridSize,
  SlidevTabProps 
} from './types';
import { PresentationList } from './PresentationList';
import { SlideEditor } from './SlideEditor';
import { PlaybackMode } from './PlaybackMode';
import { AISlideGenerator } from './AISlideGenerator';
import { SlideGrid } from './SlideGrid';
import { PresentationControls } from './PresentationControls';

const SlidevTab: React.FC<SlidevTabProps> = ({ notebookId }) => {
  // State management
  const [presentations, setPresentations] = useState<PresentationSummary[]>([]);
  const [selectedPresentation, setSelectedPresentation] = useState<Presentation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  const [editingSlide, setEditingSlide] = useState<Slide | null>(null);
  const [playbackMode, setPlaybackMode] = useState(false);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [showNotes, setShowNotes] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [gridSize, setGridSize] = useState<GridSize>('medium');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [presentationToDelete, setPresentationToDelete] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { messages } = useChatMessages();

  // Load presentations on component mount
  useEffect(() => {
    loadPresentations();
  }, []);

  // Keyboard navigation for playback mode
  useEffect(() => {
    if (!playbackMode) return;

    const handleKeyPress = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowRight':
        case ' ':
          e.preventDefault();
          handleNextSlide();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handlePrevSlide();
          break;
        case 'n':
        case 'N':
          e.preventDefault();
          setShowNotes(!showNotes);
          break;
        case 'Escape':
          e.preventDefault();
          setPlaybackMode(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [playbackMode, showNotes]);

  // API functions
  const loadPresentations = async () => {
    setIsLoading(true);
    try {
      const result = await invoke('get_presentations');
      setPresentations(result);
    } catch (error) {
      console.error('Failed to load presentations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load presentations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePresentation = async () => {
    try {
      const newPresentation: Presentation = {
        id: crypto.randomUUID(),
        title: 'New Presentation',
        description: '',
        author: '',
        theme: 'default',
        slides: [{
          id: crypto.randomUUID(),
          title: 'Welcome',
          content: '# Welcome\n\nYour presentation starts here.',
          notes: '',
          layout: 'default',
          transition: 'slide-left',
          background: '',
          class: '',
          order: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }],
        frontmatter: {
          theme: 'default',
          background: '',
          class: '',
          highlighter: 'shiki',
          lineNumbers: false,
          info: '',
          persist: false,
          exportFilename: '',
          title: 'New Presentation',
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const result = await invoke('create_presentation', { presentation: newPresentation });
      setSelectedPresentation(result);
      await loadPresentations();
      
      toast({
        title: 'Success',
        description: 'New presentation created',
      });
    } catch (error) {
      console.error('Failed to create presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to create presentation',
        variant: 'destructive',
      });
    }
  };

  const handlePresentationClick = async (presentationId: string) => {
    try {
      const presentation = await invoke('get_presentation', { id: presentationId });
      setSelectedPresentation(presentation);
    } catch (error) {
      console.error('Failed to load presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to load presentation',
        variant: 'destructive',
      });
    }
  };

  const handleUpdatePresentationTitle = async (title: string) => {
    if (!selectedPresentation) return;

    try {
      const updatedPresentation = {
        ...selectedPresentation,
        title,
        updated_at: new Date().toISOString(),
      };
      
      await invoke('update_presentation', { presentation: updatedPresentation });
      setSelectedPresentation(updatedPresentation);
      await loadPresentations();
    } catch (error) {
      console.error('Failed to update presentation title:', error);
      toast({
        title: 'Error',
        description: 'Failed to update presentation title',
        variant: 'destructive',
      });
    }
  };

  const handleDeletePresentation = async (presentationId: string) => {
    try {
      await invoke('delete_presentation', { id: presentationId });
      if (selectedPresentation?.id === presentationId) {
        setSelectedPresentation(null);
      }
      await loadPresentations();
      toast({
        title: 'Success',
        description: 'Presentation deleted',
      });
    } catch (error) {
      console.error('Failed to delete presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete presentation',
        variant: 'destructive',
      });
    }
  };

  const handleAddSlide = () => {
    if (!selectedPresentation) return;

    const newSlide: Slide = {
      id: crypto.randomUUID(),
      title: `Slide ${selectedPresentation.slides.length + 1}`,
      content: '# New Slide\n\nAdd your content here.',
      notes: '',
      layout: 'default',
      transition: 'slide-left',
      background: '',
      class: '',
      order: selectedPresentation.slides.length,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const updatedPresentation = {
      ...selectedPresentation,
      slides: [...selectedPresentation.slides, newSlide],
      updated_at: new Date().toISOString(),
    };

    setSelectedPresentation(updatedPresentation);
  };

  const handleRemoveSlide = (slideId: string) => {
    if (!selectedPresentation) return;

    const updatedSlides = selectedPresentation.slides
      .filter(slide => slide.id !== slideId)
      .map((slide, index) => ({ ...slide, order: index }));

    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString(),
    };

    setSelectedPresentation(updatedPresentation);
  };

  const handleSlidesReorder = (startIndex: number, endIndex: number) => {
    if (!selectedPresentation) return;

    const slides = [...selectedPresentation.slides];
    const [reorderedSlide] = slides.splice(startIndex, 1);
    slides.splice(endIndex, 0, reorderedSlide);

    const updatedSlides = slides.map((slide, index) => ({
      ...slide,
      order: index,
      updated_at: new Date().toISOString(),
    }));

    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString(),
    };

    setSelectedPresentation(updatedPresentation);
  };

  const handleSlideDuplicate = (slideId: string) => {
    if (!selectedPresentation) return;

    const slideIndex = selectedPresentation.slides.findIndex(s => s.id === slideId);
    if (slideIndex === -1) return;

    const originalSlide = selectedPresentation.slides[slideIndex];
    const duplicatedSlide: Slide = {
      ...originalSlide,
      id: crypto.randomUUID(),
      title: `${originalSlide.title} (Copy)`,
      order: slideIndex + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const updatedSlides = [
      ...selectedPresentation.slides.slice(0, slideIndex + 1),
      duplicatedSlide,
      ...selectedPresentation.slides.slice(slideIndex + 1).map(slide => ({
        ...slide,
        order: slide.order + 1,
      })),
    ];

    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString(),
    };

    setSelectedPresentation(updatedPresentation);
  };

  const handleSlideUpdate = (updatedSlide: Slide) => {
    if (!selectedPresentation) return;

    const updatedSlides = selectedPresentation.slides.map(slide =>
      slide.id === updatedSlide.id ? { ...updatedSlide, updated_at: new Date().toISOString() } : slide
    );

    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString(),
    };

    setSelectedPresentation(updatedPresentation);
    setEditingSlide(null);
  };

  const handleSavePresentation = async () => {
    if (!selectedPresentation) return;

    setIsSaving(true);
    try {
      await invoke('update_presentation', { presentation: selectedPresentation });
      await loadPresentations();
      toast({
        title: 'Success',
        description: 'Presentation saved',
      });
    } catch (error) {
      console.error('Failed to save presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to save presentation',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleExportPresentation = async () => {
    if (!selectedPresentation) return;

    setIsExporting(true);
    try {
      const markdown = await invoke('export_presentation_to_markdown', { 
        presentationId: selectedPresentation.id 
      });
      
      // Create and download the file
      const blob = new Blob([markdown], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedPresentation.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Success',
        description: 'Presentation exported as Markdown',
      });
    } catch (error) {
      console.error('Failed to export presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to export presentation',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleGenerateSlides = async (request: GenerateSlidesRequest) => {
    setIsGenerating(true);
    try {
      const response = await invoke('generate_slides', { request });
      setSelectedPresentation(response.presentation);
      setShowAIGenerator(false);
      await loadPresentations();
      
      toast({
        title: 'Success',
        description: `Generated ${response.presentation.slides.length} slides`,
      });
    } catch (error) {
      console.error('Failed to generate slides:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate slides',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const generateSlidesFromChat = async () => {
    if (!messages || messages.length === 0) {
      toast({
        title: 'No Chat Messages',
        description: 'No chat messages found to generate slides from.',
        variant: 'destructive',
      });
      return;
    }

    const chatContent = messages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n\n');

    const request: GenerateSlidesRequest = {
      prompt: 'Create a presentation based on this conversation',
      context: chatContent,
      theme: 'default',
      slide_count: Math.min(Math.max(Math.floor(messages.length / 2), 5), 15),
      audience: 'General',
      duration: 15,
    };

    await handleGenerateSlides(request);
  };

  const handleImportPresentation = async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: 'Presentation Files',
          extensions: ['json', 'md']
        }]
      });

      if (!selected) return;
      
      const filePath = Array.isArray(selected) ? selected[0] : selected;
      const content = await readTextFile(filePath);
      let importedPresentation: Presentation;

      if (filePath.endsWith('.json')) {
        importedPresentation = JSON.parse(content);
      } else {
        // Parse markdown file
        const result = await invoke('import_presentation_from_markdown', { 
          content,
          filename: 'imported-presentation'
        });
        importedPresentation = result;
      }

      // Ensure the presentation has a unique ID
      importedPresentation.id = crypto.randomUUID();
      importedPresentation.created_at = new Date().toISOString();
      importedPresentation.updated_at = new Date().toISOString();

      const result = await invoke('create_presentation', { 
        presentation: importedPresentation 
      });
      
      setSelectedPresentation(result);
      await loadPresentations();
      
      toast({
        title: 'Success',
        description: 'Presentation imported successfully',
      });
    } catch (error) {
      console.error('Failed to import presentation:', error);
      toast({
        title: 'Error',
        description: 'Failed to import presentation',
        variant: 'destructive',
      });
    }
  };

  // Playback navigation
  const handleNextSlide = () => {
    if (!selectedPresentation) return;
    setCurrentSlideIndex(prev => 
      Math.min(prev + 1, selectedPresentation.slides.length - 1)
    );
  };

  const handlePrevSlide = () => {
    setCurrentSlideIndex(prev => Math.max(prev - 1, 0));
  };

  const handleSlideClick = (index: number) => {
    setCurrentSlideIndex(index);
  };

  // Render playback mode
  if (playbackMode && selectedPresentation) {
    return (
      <PlaybackMode
        isActive={playbackMode}
        presentation={selectedPresentation}
        currentSlide={currentSlideIndex}
        showPresenterNotes={showNotes}
        onPreviousSlide={handlePrevSlide}
        onNextSlide={handleNextSlide}
        onSlideChange={handleSlideClick}
        onToggleNotes={() => setShowNotes(!showNotes)}
        onExit={() => setPlaybackMode(false)}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Main Content */}
      {!selectedPresentation ? (
        <div className="flex-1 p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold">Slidev Presentations</h2>
              <p className="text-muted-foreground">Create and manage your presentations</p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleImportPresentation}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Import
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowAIGenerator(!showAIGenerator)}
                className="flex items-center gap-2"
              >
                <Bot className="h-4 w-4" />
                AI Generate
              </Button>
              
              <Button
                onClick={handleCreatePresentation}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                New Presentation
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={loadPresentations}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* AI Slide Generator */}
          <AISlideGenerator
            isVisible={showAIGenerator}
            isGenerating={isGenerating}
            onGenerate={handleGenerateSlides}
            onGenerateFromChat={generateSlidesFromChat}
            onClose={() => setShowAIGenerator(false)}
          />

          {/* Presentations List */}
          {presentations.length === 0 && !isLoading ? (
            <div className="text-center py-8">
              <Plus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No presentations yet</h3>
              <p className="text-sm text-muted-foreground mb-4">Create your first presentation to get started</p>
              <Button onClick={handleCreatePresentation}>
                <Plus className="h-4 w-4 mr-2" />
                Create Presentation
              </Button>
            </div>
          ) : (
            <PresentationList
              presentations={presentations}
              onPresentationClick={(presentation) => handlePresentationClick(presentation.id)}
              onDeletePresentation={(id: string) => {
                setPresentationToDelete(id);
                setDeleteDialogOpen(true);
              }}
            />
          )}
        </div>
      ) : (
        <div className="flex-1 flex flex-col">
          {/* Presentation Controls */}
          <PresentationControls
            onPresent={() => setPlaybackMode(true)}
            onAddSlide={handleAddSlide}
            onSave={handleSavePresentation}
            onExport={handleExportPresentation}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            gridSize={gridSize}
            onGridSizeChange={setGridSize}
            hasSlides={selectedPresentation.slides.length > 0}
            isSaving={isSaving}
            isExporting={isExporting}
          />

          {/* Slides Grid */}
          <ScrollArea className="flex-1 p-6">
            <div className="mb-4">
              <h1 className="text-2xl font-bold mb-2">{selectedPresentation.title}</h1>
              <p className="text-muted-foreground">
                {selectedPresentation.slides.length} slide{selectedPresentation.slides.length !== 1 ? 's' : ''}
              </p>
            </div>
            
            <SlideGrid
              slides={selectedPresentation.slides}
              viewMode={viewMode}
              gridSize={gridSize}
              onSlideEdit={setEditingSlide}
              onSlideDuplicate={handleSlideDuplicate}
              onSlideDelete={handleRemoveSlide}
              onSlidesReorder={handleSlidesReorder}
            />
          </ScrollArea>
        </div>
      )}

      {/* Slide Editor Dialog */}
        {editingSlide && (
          <SlideEditor
            isOpen={!!editingSlide}
            slide={editingSlide}
            slideTitle={editingSlide.title}
            slideContent={editingSlide.content}
            slideNotes={editingSlide.notes || ''}
            slideLayout={editingSlide.layout || 'default'}
            slideTransition={typeof editingSlide.transition === 'string' ? editingSlide.transition : (editingSlide.transition as any)?.type || 'fade'}
            slideBackground={editingSlide.background || ''}
            slideClass={editingSlide.class || ''}
            showPresenterNotes={showNotes}
            slideLayouts={[]}
            slideTransitions={[]}
            onTitleChange={(title) => setEditingSlide({...editingSlide, title})}
            onContentChange={(content) => setEditingSlide({...editingSlide, content})}
            onNotesChange={(notes) => setEditingSlide({...editingSlide, notes})}
            onLayoutChange={(layout) => setEditingSlide({...editingSlide, layout})}
            onTransitionChange={(transition) => setEditingSlide({...editingSlide, transition})}
            onBackgroundChange={(background) => setEditingSlide({...editingSlide, background})}
            onClassChange={(className) => setEditingSlide({...editingSlide, class: className})}
            onShowNotesChange={(show) => setShowNotes(show)}
            onSave={() => {
              handleSlideUpdate(editingSlide);
              setEditingSlide(null);
            }}
            onCancel={() => setEditingSlide(null)}
          />
        )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Presentation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this presentation? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setDeleteDialogOpen(false);
              setPresentationToDelete(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (presentationToDelete) {
                  handleDeletePresentation(presentationToDelete);
                }
                setDeleteDialogOpen(false);
                setPresentationToDelete(null);
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Hidden file input for import */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json,.md"
        style={{ display: 'none' }}
        onChange={handleImportPresentation}
      />
    </div>
  );
};

export default SlidevTab;