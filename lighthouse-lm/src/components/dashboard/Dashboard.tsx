import React, { useState, useEffect } from 'react';
import DashboardHeader from './DashboardHeader';
import NotebookGrid from '../notebook/NotebookGrid';
import EmptyDashboard from './EmptyDashboard';
import { useNotebooks, useSources } from '../../hooks';
import { useNotebookGeneration } from '../../hooks/useNotebookGeneration';
import { GridSkeleton } from '../shared/LoadingSkeleton';
import { DASHBOARD_CONSTANTS } from './constants';
import OnboardingSystem from '../onboarding/OnboardingSystem';
import HelpSystem from '../onboarding/HelpSystem';
import SearchBar from '../search/SearchBar';
import CommandSearch from '../search/CommandSearch';
import NotificationCenter, { Notification } from '../notifications/NotificationCenter';
import KeyboardShortcuts from '../shortcuts/KeyboardShortcuts';
import QuickActions from '../quickactions/QuickActions';
import { AnalyticsWidget, RecentActivityWidget, ProductivityWidget, QuickActionsWidget } from './widgets';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Button } from '../ui/button';
import { LayoutDashboard, FileText, BarChart3, Zap } from 'lucide-react';
import { tauriService } from '../../services/tauriService';
import MainNavigation from '../layout/MainNavigation';

interface DashboardProps {
  onNotebookSelect?: (notebookId: string) => void;
}

type DashboardView = 'notebooks' | 'analytics' | 'activity';

const Dashboard: React.FC<DashboardProps> = ({ onNotebookSelect }) => {
  const { notebooks, isLoading, error, isError, createNotebook } = useNotebooks();
  const { generateNotebookContent } = useNotebookGeneration();
  const hasNotebooks = notebooks && notebooks.length > 0;
  
  // UI State
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeView, setActiveView] = useState<DashboardView>('notebooks');
  const [showHelpSystem, setShowHelpSystem] = useState(false);
  const [commandOpen, setCommandOpen] = useState(false);
  
  // Check if user needs onboarding
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const onboardingProgress = await tauriService.getAppState('lighthouse-onboarding-progress');
        const progress = onboardingProgress ? JSON.parse(onboardingProgress) : null;
        
        if (!progress?.onboardingCompleted && !hasNotebooks && !isLoading) {
          setShowOnboarding(true);
        }
      } catch (error) {
        console.warn('Failed to load onboarding progress from backend, using localStorage fallback:', error);
        // Fallback to localStorage for backward compatibility
        const localProgress = localStorage.getItem('lighthouse-onboarding-progress');
        const progress = localProgress ? JSON.parse(localProgress) : null;
        
        if (!progress?.onboardingCompleted && !hasNotebooks && !isLoading) {
          setShowOnboarding(true);
        }
      }
    };
    
    checkOnboardingStatus();
  }, [hasNotebooks, isLoading]);

  const handleOnboardingComplete = (): void => {
    setShowOnboarding(false);
  };

  const handleOnboardingSkip = (): void => {
    setShowOnboarding(false);
  };

  const handleSearch = (query: string, filters?: any): void => {
    setSearchQuery(query);
    // Search is handled by filtering the notebooks array in the render method
  };

  const handleMarkNotificationRead = (id: string): void => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const handleMarkAllNotificationsRead = (): void => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const handleClearNotification = (id: string): void => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const handleClearAllNotifications = (): void => {
    setNotifications([]);
  };

  // Quick action handlers
  const handleCreateNotebook = (): void => {
    createNotebook({
      title: "New Notebook",
      description: "Created from dashboard"
    });
  };

  const handleUploadSource = (): void => {
    // This would typically open a file dialog or source management modal
    const event = new CustomEvent('open-source-management');
    window.dispatchEvent(event);
  };

  const handleStartChat = (): void => {
    // This would typically navigate to or open a chat interface
  };

  const handleGenerateDiagram = (): void => {
    // This would typically open diagram generation interface
  };

  const handleOpenSearch = (): void => {
    document.getElementById('lighthouse-search')?.focus();
  };

  const handleImportFromWeb = (): void => {
    // This would typically open a web import dialog
  };

  // Show notebooks loading state with skeleton
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <DashboardHeader onHelpClick={() => setShowHelpSystem(true)} />
        <main className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
          <GridSkeleton count={6} />
        </main>
      </div>
    );
  }

  // Show notebooks error if present
  if (isError && error) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DashboardHeader onHelpClick={() => setShowHelpSystem(true)} />
        <main className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
          <div className="mb-8">
            <h1 className="text-4xl font-medium text-foreground mb-2">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
          </div>
          <div className="text-center py-16">
            <p className="text-destructive">Error loading notebooks: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <div className="flex flex-col min-h-0">
        {/* Command Search */}
        <CommandSearch 
          open={commandOpen} 
          setOpen={setCommandOpen}
        />
        
        <DashboardHeader onHelpClick={() => setShowHelpSystem(true)}>
          {/* Add Search Bar and Notification Center to header */}
          <div className="flex items-center gap-4">
            <SearchBar
              onSearch={handleSearch}
              className="w-96"
              suggestions={[]}
              recentSearches={[]}
            />
            <NotificationCenter
              notifications={notifications}
              onMarkAsRead={handleMarkNotificationRead}
              onMarkAllAsRead={handleMarkAllNotificationsRead}
              onClear={handleClearNotification}
              onClearAll={handleClearAllNotifications}
            />
          </div>
        </DashboardHeader>
        
        <main className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 flex-1">
          <div className="mb-6 sm:mb-8">
            <h1 className="font-medium text-foreground mb-2 text-3xl sm:text-4xl lg:text-5xl">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
          </div>

          {hasNotebooks ? (
            <>
              {/* Dashboard Navigation Tabs */}
              <Tabs value={activeView} onValueChange={(value) => setActiveView(value as DashboardView)} className="mb-6 sm:mb-8">
                <TabsList className="grid w-full grid-cols-3 max-w-full sm:max-w-2xl gap-1 sm:gap-0">
                  <TabsTrigger value="notebooks" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                    <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden xs:inline sm:inline">Notebooks</span>
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                    <BarChart3 className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden xs:inline sm:inline">Analytics</span>
                  </TabsTrigger>
                  <TabsTrigger value="activity" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                    <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden xs:inline sm:inline">Activity</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="notebooks" className="space-y-4 sm:space-y-6 mt-6 sm:mt-8">
                  {/* Search and Filters */}
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 sm:gap-4">
                    <SearchBar 
                      onSearch={handleSearch}
                      placeholder="Search notebooks..."
                      className="w-full sm:max-w-md"
                    />
                    <Button onClick={handleCreateNotebook} className="w-full sm:w-auto">
                      Create Notebook
                    </Button>
                  </div>
                  
                  {/* Notebook Grid with Search Filtering */}
                   <NotebookGrid onNotebookSelect={onNotebookSelect} />
                </TabsContent>

                <TabsContent value="analytics" className="space-y-6 mt-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <AnalyticsWidget className="lg:col-span-2" />
                    <ProductivityWidget />
                    <div className="space-y-6">
                      <RecentActivityWidget />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="activity" className="space-y-6 mt-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <RecentActivityWidget className="lg:col-span-2" />
                    <div className="space-y-6">
                      <QuickActionsWidget
                        onCreateNotebook={handleCreateNotebook}
                        onUploadSource={handleUploadSource}
                        onStartChat={handleStartChat}
                        onOpenSearch={handleOpenSearch}
                        onImportWeb={handleImportFromWeb}
                        onGenerateDiagram={handleGenerateDiagram}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          ) : (
            <EmptyDashboard />
          )}
        </main>

        {/* Onboarding System */}
        {showOnboarding && (
          <OnboardingSystem
            onComplete={handleOnboardingComplete}
            onSkip={handleOnboardingSkip}
          />
        )}

        {/* Keyboard Shortcuts Modal */}
        <KeyboardShortcuts
          isOpen={showKeyboardShortcuts}
          onClose={() => setShowKeyboardShortcuts(false)}
        />

        {/* Quick Actions Floating Menu */}
        <QuickActions
          onCreateNotebook={handleCreateNotebook}
          onUploadSource={handleUploadSource}
          onStartChat={handleStartChat}
          onGenerateDiagram={handleGenerateDiagram}
          onOpenSearch={handleOpenSearch}
          onImportFromWeb={handleImportFromWeb}
        />

        {/* Help System */}
        <HelpSystem
          isOpen={showHelpSystem}
          onClose={() => setShowHelpSystem(false)}
        />
      </div>
    </div>
  );
};

export default Dashboard;