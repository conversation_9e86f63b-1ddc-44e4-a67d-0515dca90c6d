{"hash": "74aa10d5", "configHash": "58300c4b", "lockfileHash": "fd790ee3", "browserHash": "d25d7af5", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e2cdba74", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c0b5375e", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "eeda73a8", "needsInterop": false}, "react-error-boundary": {"src": "../../react-error-boundary/dist/react-error-boundary.development.esm.js", "file": "react-error-boundary.js", "fileHash": "02a63c50", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "be03e617", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "c0bde772", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d07473bc", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "ffcba0ed", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "96bf4603", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "b150c76f", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d190c8ea", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "09d14c25", "needsInterop": true}, "@radix-ui/react-alert-dialog": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "c7fa2c8e", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "cf2f7737", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "d137694a", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "3a5cbfc5", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "4081ffe1", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "c3bf60c6", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "2017f3c0", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "2bf1f0b4", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "e2cb42d6", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "a1d774b9", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "2d63ea45", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "22369fc7", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "b9fa84cf", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "425d02a4", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../../lighthouse-lm/node_modules/@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "0bb27586", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "e4c0dbaa", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "e7fcefda", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "ff7e604a", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "4b145697", "needsInterop": false}, "cmdk": {"src": "../../../lighthouse-lm/node_modules/cmdk/dist/index.mjs", "file": "cmdk.js", "fileHash": "6691143e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "04ab4815", "needsInterop": true}, "react-dropzone": {"src": "../../../../../node_modules/react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "5815aeeb", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "3ac2211b", "needsInterop": false}, "react-resizable-panels": {"src": "../../../lighthouse-lm/node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js", "file": "react-resizable-panels.js", "fileHash": "f7e09aef", "needsInterop": false}, "sonner": {"src": "../../../lighthouse-lm/node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "71a04445", "needsInterop": false}}, "chunks": {"chunk-W6PRXJXX": {"file": "chunk-W6PRXJXX.js"}, "chunk-4RO2SXZU": {"file": "chunk-4RO2SXZU.js"}, "chunk-K4PXLHHK": {"file": "chunk-K4PXLHHK.js"}, "chunk-GF3PI5EE": {"file": "chunk-GF3PI5EE.js"}, "chunk-NPGFVZ4N": {"file": "chunk-NPGFVZ4N.js"}, "chunk-SST2F6SQ": {"file": "chunk-SST2F6SQ.js"}, "chunk-NLX6X2PU": {"file": "chunk-NLX6X2PU.js"}, "chunk-THLQFQI7": {"file": "chunk-THLQFQI7.js"}, "chunk-VPINLEXQ": {"file": "chunk-VPINLEXQ.js"}, "chunk-RWWRQIGE": {"file": "chunk-RWWRQIGE.js"}, "chunk-7V5RXJBF": {"file": "chunk-7V5RXJBF.js"}, "chunk-KM2NAYD6": {"file": "chunk-KM2NAYD6.js"}, "chunk-YZSRZJ24": {"file": "chunk-YZSRZJ24.js"}, "chunk-3UHT7YHH": {"file": "chunk-3UHT7YHH.js"}, "chunk-MT3KAEF7": {"file": "chunk-MT3KAEF7.js"}, "chunk-2DKABOL4": {"file": "chunk-2DKABOL4.js"}, "chunk-ASF6GUNW": {"file": "chunk-ASF6GUNW.js"}, "chunk-32NQRJCS": {"file": "chunk-32NQRJCS.js"}, "chunk-UQNUEBLV": {"file": "chunk-UQNUEBLV.js"}, "chunk-VPXE2U7P": {"file": "chunk-VPXE2U7P.js"}, "chunk-AL5TBHVP": {"file": "chunk-AL5TBHVP.js"}, "chunk-VSCWG6HG": {"file": "chunk-VSCWG6HG.js"}, "chunk-ZBTRVDWR": {"file": "chunk-ZBTRVDWR.js"}, "chunk-DWRXVABK": {"file": "chunk-DWRXVABK.js"}, "chunk-77BZSBEB": {"file": "chunk-77BZSBEB.js"}, "chunk-DHHXW66K": {"file": "chunk-DHHXW66K.js"}, "chunk-56NJSAX2": {"file": "chunk-56NJSAX2.js"}, "chunk-3ZX5V5AC": {"file": "chunk-3ZX5V5AC.js"}, "chunk-SXRIVT2P": {"file": "chunk-SXRIVT2P.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}