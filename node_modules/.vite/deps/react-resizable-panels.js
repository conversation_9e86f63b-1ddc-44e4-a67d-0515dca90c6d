import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// lighthouse-lm/node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js
var React = __toESM(require_react());
var {
  createElement,
  createContext,
  createRef,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} = React;
var useId = React["useId".toString()];
var PanelGroupContext = createContext(null);
PanelGroupContext.displayName = "PanelGroupContext";
var useIsomorphicLayoutEffect = useLayoutEffect;
var wrappedUseId = typeof useId === "function" ? useId : () => null;
var counter = 0;
function useUniqueId(idFromParams = null) {
  const idFromUseId = wrappedUseId();
  const idRef = useRef(idFromParams || idFromUseId || null);
  if (idRef.current === null) {
    idRef.current = "" + counter++;
  }
  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;
}
function PanelWithForwardedRef({
  children,
  className: classNameFromProps = "",
  collapsedSizePercentage,
  collapsedSizePixels,
  collapsible,
  dataAttributes,
  defaultSizePercentage,
  defaultSizePixels,
  forwardedRef,
  id: idFromProps,
  maxSizePercentage,
  maxSizePixels,
  minSizePercentage,
  minSizePixels,
  onCollapse,
  onExpand,
  onResize,
  order,
  style: styleFromProps,
  tagName: Type = "div"
}) {
  const context = useContext(PanelGroupContext);
  if (context === null) {
    throw Error(`Panel components must be rendered within a PanelGroup container`);
  }
  const {
    collapsePanel,
    expandPanel,
    getPanelSize,
    getPanelStyle,
    groupId,
    isPanelCollapsed,
    registerPanel,
    resizePanel: resizePanel2,
    unregisterPanel
  } = context;
  const panelId = useUniqueId(idFromProps);
  const panelDataRef = useRef({
    callbacks: {
      onCollapse,
      onExpand,
      onResize
    },
    constraints: {
      collapsedSizePercentage,
      collapsedSizePixels,
      collapsible,
      defaultSizePercentage,
      defaultSizePixels,
      maxSizePercentage,
      maxSizePixels,
      minSizePercentage,
      minSizePixels
    },
    id: panelId,
    idIsFromProps: idFromProps !== void 0,
    order
  });
  const devWarningsRef = useRef({
    didLogMissingDefaultSizeWarning: false
  });
  {
    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) ;
  }
  useIsomorphicLayoutEffect(() => {
    const {
      callbacks,
      constraints
    } = panelDataRef.current;
    panelDataRef.current.id = panelId;
    panelDataRef.current.idIsFromProps = idFromProps !== void 0;
    panelDataRef.current.order = order;
    callbacks.onCollapse = onCollapse;
    callbacks.onExpand = onExpand;
    callbacks.onResize = onResize;
    constraints.collapsedSizePercentage = collapsedSizePercentage;
    constraints.collapsedSizePixels = collapsedSizePixels;
    constraints.collapsible = collapsible;
    constraints.defaultSizePercentage = defaultSizePercentage;
    constraints.defaultSizePixels = defaultSizePixels;
    constraints.maxSizePercentage = maxSizePercentage;
    constraints.maxSizePixels = maxSizePixels;
    constraints.minSizePercentage = minSizePercentage;
    constraints.minSizePixels = minSizePixels;
  });
  useIsomorphicLayoutEffect(() => {
    const panelData = panelDataRef.current;
    registerPanel(panelData);
    return () => {
      unregisterPanel(panelData);
    };
  }, [order, panelId, registerPanel, unregisterPanel]);
  useImperativeHandle(forwardedRef, () => ({
    collapse: () => {
      collapsePanel(panelDataRef.current);
    },
    expand: () => {
      expandPanel(panelDataRef.current);
    },
    getId() {
      return panelId;
    },
    getSize() {
      return getPanelSize(panelDataRef.current);
    },
    isCollapsed() {
      return isPanelCollapsed(panelDataRef.current);
    },
    isExpanded() {
      return !isPanelCollapsed(panelDataRef.current);
    },
    resize: (mixedSizes) => {
      resizePanel2(panelDataRef.current, mixedSizes);
    }
  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel2]);
  const style = getPanelStyle(panelDataRef.current);
  return createElement(Type, {
    children,
    className: classNameFromProps,
    style: {
      ...style,
      ...styleFromProps
    },
    ...dataAttributes,
    // CSS selectors
    "data-panel": "",
    "data-panel-id": panelId,
    "data-panel-group-id": groupId,
    // e2e test attributes
    "data-panel-collapsible": collapsible || void 0,
    "data-panel-size": parseFloat("" + style.flexGrow).toFixed(1)
  });
}
var Panel = forwardRef((props, ref) => createElement(PanelWithForwardedRef, {
  ...props,
  forwardedRef: ref
}));
PanelWithForwardedRef.displayName = "Panel";
Panel.displayName = "forwardRef(Panel)";
function convertPixelsToPercentage(pixels, groupSizePixels) {
  return pixels / groupSizePixels * 100;
}
function convertPixelConstraintsToPercentages(panelConstraints, groupSizePixels) {
  let {
    collapsedSizePercentage = 0,
    collapsedSizePixels,
    defaultSizePercentage,
    defaultSizePixels,
    maxSizePercentage = 100,
    maxSizePixels,
    minSizePercentage = 0,
    minSizePixels
  } = panelConstraints;
  const hasPixelConstraints = collapsedSizePixels != null || defaultSizePixels != null || minSizePixels != null || maxSizePixels != null;
  if (hasPixelConstraints && groupSizePixels <= 0) {
    console.warn(`WARNING: Invalid group size: ${groupSizePixels}px`);
    return {
      collapsedSizePercentage: 0,
      defaultSizePercentage,
      maxSizePercentage: 0,
      minSizePercentage: 0
    };
  }
  if (collapsedSizePixels != null) {
    collapsedSizePercentage = convertPixelsToPercentage(collapsedSizePixels, groupSizePixels);
  }
  if (defaultSizePixels != null) {
    defaultSizePercentage = convertPixelsToPercentage(defaultSizePixels, groupSizePixels);
  }
  if (minSizePixels != null) {
    minSizePercentage = convertPixelsToPercentage(minSizePixels, groupSizePixels);
  }
  if (maxSizePixels != null) {
    maxSizePercentage = convertPixelsToPercentage(maxSizePixels, groupSizePixels);
  }
  return {
    collapsedSizePercentage,
    defaultSizePercentage,
    maxSizePercentage,
    minSizePercentage
  };
}
function computePercentagePanelConstraints(panelConstraintsArray, panelIndex, groupSizePixels) {
  let totalMinConstraints = 0;
  let totalMaxConstraints = 0;
  for (let index = 0; index < panelConstraintsArray.length; index++) {
    if (index !== panelIndex) {
      const {
        collapsible
      } = panelConstraintsArray[index];
      const {
        collapsedSizePercentage: collapsedSizePercentage2,
        maxSizePercentage: maxSizePercentage2,
        minSizePercentage: minSizePercentage2
      } = convertPixelConstraintsToPercentages(panelConstraintsArray[index], groupSizePixels);
      totalMaxConstraints += maxSizePercentage2;
      totalMinConstraints += collapsible ? collapsedSizePercentage2 : minSizePercentage2;
    }
  }
  const {
    collapsedSizePercentage,
    defaultSizePercentage,
    maxSizePercentage,
    minSizePercentage
  } = convertPixelConstraintsToPercentages(panelConstraintsArray[panelIndex], groupSizePixels);
  return {
    collapsedSizePercentage,
    defaultSizePercentage,
    maxSizePercentage: panelConstraintsArray.length > 1 ? Math.min(maxSizePercentage, 100 - totalMinConstraints) : maxSizePercentage,
    minSizePercentage: panelConstraintsArray.length > 1 ? Math.max(minSizePercentage, 100 - totalMaxConstraints) : minSizePercentage
  };
}
var PRECISION = 10;
function fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {
  actual = parseFloat(actual.toFixed(fractionDigits));
  expected = parseFloat(expected.toFixed(fractionDigits));
  const delta = actual - expected;
  if (delta === 0) {
    return 0;
  } else {
    return delta > 0 ? 1 : -1;
  }
}
function fuzzyNumbersEqual(actual, expected, fractionDigits) {
  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;
}
function resizePanel({
  groupSizePixels,
  panelConstraints,
  panelIndex,
  size
}) {
  const hasPixelConstraints = panelConstraints.some(({
    collapsedSizePixels,
    defaultSizePixels,
    minSizePixels,
    maxSizePixels
  }) => collapsedSizePixels != null || defaultSizePixels != null || minSizePixels != null || maxSizePixels != null);
  if (hasPixelConstraints && groupSizePixels <= 0) {
    console.warn(`WARNING: Invalid group size: ${groupSizePixels}px`);
    return 0;
  }
  let {
    collapsible
  } = panelConstraints[panelIndex];
  const {
    collapsedSizePercentage,
    maxSizePercentage,
    minSizePercentage
  } = computePercentagePanelConstraints(panelConstraints, panelIndex, groupSizePixels);
  if (minSizePercentage != null) {
    if (fuzzyCompareNumbers(size, minSizePercentage) < 0) {
      if (collapsible) {
        const halfwayPoint = (collapsedSizePercentage + minSizePercentage) / 2;
        if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {
          size = collapsedSizePercentage;
        } else {
          size = minSizePercentage;
        }
      } else {
        size = minSizePercentage;
      }
    }
  }
  if (maxSizePercentage != null) {
    size = Math.min(maxSizePercentage, size);
  }
  return size;
}
function adjustLayoutByDelta({
  delta,
  groupSizePixels,
  layout: prevLayout,
  panelConstraints,
  pivotIndices,
  trigger
}) {
  if (fuzzyNumbersEqual(delta, 0)) {
    return prevLayout;
  }
  const nextLayout = [...prevLayout];
  let deltaApplied = 0;
  {
    if (trigger === "keyboard") {
      {
        const index = delta < 0 ? pivotIndices[1] : pivotIndices[0];
        const constraints = panelConstraints[index];
        if (constraints.collapsible) {
          const prevSize = prevLayout[index];
          const {
            collapsedSizePercentage,
            minSizePercentage
          } = computePercentagePanelConstraints(panelConstraints, index, groupSizePixels);
          if (fuzzyNumbersEqual(prevSize, collapsedSizePercentage)) {
            const localDelta = minSizePercentage - prevSize;
            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {
              delta = delta < 0 ? 0 - localDelta : localDelta;
            }
          }
        }
      }
      {
        const index = delta < 0 ? pivotIndices[0] : pivotIndices[1];
        const constraints = panelConstraints[index];
        if (constraints.collapsible) {
          const prevSize = prevLayout[index];
          const {
            collapsedSizePercentage,
            minSizePercentage
          } = computePercentagePanelConstraints(panelConstraints, index, groupSizePixels);
          if (fuzzyNumbersEqual(prevSize, minSizePercentage)) {
            const localDelta = prevSize - collapsedSizePercentage;
            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {
              delta = delta < 0 ? 0 - localDelta : localDelta;
            }
          }
        }
      }
    }
  }
  {
    const increment = delta < 0 ? 1 : -1;
    let index = delta < 0 ? pivotIndices[1] : pivotIndices[0];
    let maxAvailableDelta = 0;
    while (true) {
      const prevSize = prevLayout[index];
      const maxSafeSize = resizePanel({
        groupSizePixels,
        panelConstraints,
        panelIndex: index,
        size: 100
      });
      const delta2 = maxSafeSize - prevSize;
      maxAvailableDelta += delta2;
      index += increment;
      if (index < 0 || index >= panelConstraints.length) {
        break;
      }
    }
    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));
    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;
  }
  {
    const pivotIndex = delta < 0 ? pivotIndices[0] : pivotIndices[1];
    let index = pivotIndex;
    while (index >= 0 && index < panelConstraints.length) {
      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);
      const prevSize = prevLayout[index];
      const unsafeSize = prevSize - deltaRemaining;
      const safeSize = resizePanel({
        groupSizePixels,
        panelConstraints,
        panelIndex: index,
        size: unsafeSize
      });
      if (!fuzzyNumbersEqual(prevSize, safeSize)) {
        deltaApplied += prevSize - safeSize;
        nextLayout[index] = safeSize;
        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), void 0, {
          numeric: true
        }) >= 0) {
          break;
        }
      }
      if (delta < 0) {
        index--;
      } else {
        index++;
      }
    }
  }
  if (fuzzyNumbersEqual(deltaApplied, 0)) {
    return prevLayout;
  }
  {
    const pivotIndex = delta < 0 ? pivotIndices[1] : pivotIndices[0];
    const unsafeSize = prevLayout[pivotIndex] + deltaApplied;
    const safeSize = resizePanel({
      groupSizePixels,
      panelConstraints,
      panelIndex: pivotIndex,
      size: unsafeSize
    });
    nextLayout[pivotIndex] = safeSize;
    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {
      let deltaRemaining = unsafeSize - safeSize;
      const pivotIndex2 = delta < 0 ? pivotIndices[1] : pivotIndices[0];
      let index = pivotIndex2;
      while (index >= 0 && index < panelConstraints.length) {
        const prevSize = nextLayout[index];
        const unsafeSize2 = prevSize + deltaRemaining;
        const safeSize2 = resizePanel({
          groupSizePixels,
          panelConstraints,
          panelIndex: index,
          size: unsafeSize2
        });
        if (!fuzzyNumbersEqual(prevSize, safeSize2)) {
          deltaRemaining -= safeSize2 - prevSize;
          nextLayout[index] = safeSize2;
        }
        if (fuzzyNumbersEqual(deltaRemaining, 0)) {
          break;
        }
        if (delta > 0) {
          index--;
        } else {
          index++;
        }
      }
    }
  }
  const totalSize = nextLayout.reduce((total, size) => size + total, 0);
  deltaApplied = 100 - totalSize;
  if (!fuzzyNumbersEqual(totalSize, 100)) {
    return prevLayout;
  }
  return nextLayout;
}
function assert(expectedCondition, message = "Assertion failed!") {
  if (!expectedCondition) {
    console.error(message);
    throw Error(message);
  }
}
function getPercentageSizeFromMixedSizes({
  sizePercentage,
  sizePixels
}, groupSizePixels) {
  if (sizePercentage != null) {
    return sizePercentage;
  } else if (sizePixels != null) {
    return convertPixelsToPercentage(sizePixels, groupSizePixels);
  }
  return void 0;
}
function calculateAriaValues({
  groupSizePixels,
  layout,
  panelsArray,
  pivotIndices
}) {
  let currentMinSize = 0;
  let currentMaxSize = 100;
  let totalMinSize = 0;
  let totalMaxSize = 0;
  panelsArray.forEach((panelData, index) => {
    var _getPercentageSizeFro, _getPercentageSizeFro2;
    const {
      constraints
    } = panelData;
    const {
      maxSizePercentage,
      maxSizePixels,
      minSizePercentage,
      minSizePixels
    } = constraints;
    const minSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({
      sizePercentage: minSizePercentage,
      sizePixels: minSizePixels
    }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;
    const maxSize = (_getPercentageSizeFro2 = getPercentageSizeFromMixedSizes({
      sizePercentage: maxSizePercentage,
      sizePixels: maxSizePixels
    }, groupSizePixels)) !== null && _getPercentageSizeFro2 !== void 0 ? _getPercentageSizeFro2 : 100;
    if (index === pivotIndices[0]) {
      currentMinSize = minSize;
      currentMaxSize = maxSize;
    } else {
      totalMinSize += minSize;
      totalMaxSize += maxSize;
    }
  });
  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);
  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);
  const valueNow = layout[pivotIndices[0]];
  return {
    valueMax,
    valueMin,
    valueNow
  };
}
function getResizeHandleElementsForGroup(groupId) {
  return Array.from(document.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${groupId}"]`));
}
function getResizeHandleElementIndex(groupId, id) {
  const handles = getResizeHandleElementsForGroup(groupId);
  const index = handles.findIndex((handle) => handle.getAttribute("data-panel-resize-handle-id") === id);
  return index !== null && index !== void 0 ? index : null;
}
function determinePivotIndices(groupId, dragHandleId) {
  const index = getResizeHandleElementIndex(groupId, dragHandleId);
  return index != null ? [index, index + 1] : [-1, -1];
}
function getPanelGroupElement(id) {
  const element2 = document.querySelector(`[data-panel-group][data-panel-group-id="${id}"]`);
  if (element2) {
    return element2;
  }
  return null;
}
function calculateAvailablePanelSizeInPixels(groupId) {
  const panelGroupElement = getPanelGroupElement(groupId);
  if (panelGroupElement == null) {
    return NaN;
  }
  const direction = panelGroupElement.getAttribute("data-panel-group-direction");
  const resizeHandles = getResizeHandleElementsForGroup(groupId);
  if (direction === "horizontal") {
    return panelGroupElement.offsetWidth - resizeHandles.reduce((accumulated, handle) => {
      return accumulated + handle.offsetWidth;
    }, 0);
  } else {
    return panelGroupElement.offsetHeight - resizeHandles.reduce((accumulated, handle) => {
      return accumulated + handle.offsetHeight;
    }, 0);
  }
}
function getAvailableGroupSizePixels(groupId) {
  const panelGroupElement = getPanelGroupElement(groupId);
  if (panelGroupElement == null) {
    return NaN;
  }
  const direction = panelGroupElement.getAttribute("data-panel-group-direction");
  const resizeHandles = getResizeHandleElementsForGroup(groupId);
  if (direction === "horizontal") {
    return panelGroupElement.offsetWidth - resizeHandles.reduce((accumulated, handle) => {
      return accumulated + handle.offsetWidth;
    }, 0);
  } else {
    return panelGroupElement.offsetHeight - resizeHandles.reduce((accumulated, handle) => {
      return accumulated + handle.offsetHeight;
    }, 0);
  }
}
function getResizeHandleElement(id) {
  const element2 = document.querySelector(`[data-panel-resize-handle-id="${id}"]`);
  if (element2) {
    return element2;
  }
  return null;
}
function getResizeHandlePanelIds(groupId, handleId, panelsArray) {
  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;
  const handle = getResizeHandleElement(handleId);
  const handles = getResizeHandleElementsForGroup(groupId);
  const index = handle ? handles.indexOf(handle) : -1;
  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;
  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;
  return [idBefore, idAfter];
}
function useWindowSplitterPanelGroupBehavior({
  committedValuesRef,
  eagerValuesRef,
  groupId,
  layout,
  panelDataArray,
  setLayout
}) {
  const devWarningsRef = useRef({
    didWarnAboutMissingResizeHandle: false
  });
  useIsomorphicLayoutEffect(() => {
    const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);
    const resizeHandleElements = getResizeHandleElementsForGroup(groupId);
    for (let index = 0; index < panelDataArray.length - 1; index++) {
      const {
        valueMax,
        valueMin,
        valueNow
      } = calculateAriaValues({
        groupSizePixels,
        layout,
        panelsArray: panelDataArray,
        pivotIndices: [index, index + 1]
      });
      const resizeHandleElement = resizeHandleElements[index];
      if (resizeHandleElement == null) {
        {
          const {
            didWarnAboutMissingResizeHandle
          } = devWarningsRef.current;
          if (!didWarnAboutMissingResizeHandle) {
            devWarningsRef.current.didWarnAboutMissingResizeHandle = true;
            console.warn(`WARNING: Missing resize handle for PanelGroup "${groupId}"`);
          }
        }
      } else {
        resizeHandleElement.setAttribute("aria-controls", panelDataArray[index].id);
        resizeHandleElement.setAttribute("aria-valuemax", "" + Math.round(valueMax));
        resizeHandleElement.setAttribute("aria-valuemin", "" + Math.round(valueMin));
        resizeHandleElement.setAttribute("aria-valuenow", "" + Math.round(valueNow));
      }
    }
    return () => {
      resizeHandleElements.forEach((resizeHandleElement, index) => {
        resizeHandleElement.removeAttribute("aria-controls");
        resizeHandleElement.removeAttribute("aria-valuemax");
        resizeHandleElement.removeAttribute("aria-valuemin");
        resizeHandleElement.removeAttribute("aria-valuenow");
      });
    };
  }, [groupId, layout, panelDataArray]);
  useEffect(() => {
    const {
      panelDataArray: panelDataArray2
    } = eagerValuesRef.current;
    const groupElement = getPanelGroupElement(groupId);
    assert(groupElement != null, `No group found for id "${groupId}"`);
    const handles = getResizeHandleElementsForGroup(groupId);
    const cleanupFunctions = handles.map((handle) => {
      const handleId = handle.getAttribute("data-panel-resize-handle-id");
      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray2);
      if (idBefore == null || idAfter == null) {
        return () => {
        };
      }
      const onKeyDown = (event) => {
        if (event.defaultPrevented) {
          return;
        }
        switch (event.key) {
          case "Enter": {
            event.preventDefault();
            const index = panelDataArray2.findIndex((panelData) => panelData.id === idBefore);
            if (index >= 0) {
              const panelData = panelDataArray2[index];
              const size = layout[index];
              if (size != null && panelData.constraints.collapsible) {
                var _getPercentageSizeFro, _getPercentageSizeFro2;
                const groupSizePixels = getAvailableGroupSizePixels(groupId);
                const collapsedSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({
                  sizePercentage: panelData.constraints.collapsedSizePercentage,
                  sizePixels: panelData.constraints.collapsedSizePixels
                }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;
                const minSize = (_getPercentageSizeFro2 = getPercentageSizeFromMixedSizes({
                  sizePercentage: panelData.constraints.minSizePercentage,
                  sizePixels: panelData.constraints.minSizePixels
                }, groupSizePixels)) !== null && _getPercentageSizeFro2 !== void 0 ? _getPercentageSizeFro2 : 0;
                const nextLayout = adjustLayoutByDelta({
                  delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,
                  groupSizePixels,
                  layout,
                  panelConstraints: panelDataArray2.map((panelData2) => panelData2.constraints),
                  pivotIndices: determinePivotIndices(groupId, handleId),
                  trigger: "keyboard"
                });
                if (layout !== nextLayout) {
                  setLayout(nextLayout);
                }
              }
            }
            break;
          }
        }
      };
      handle.addEventListener("keydown", onKeyDown);
      return () => {
        handle.removeEventListener("keydown", onKeyDown);
      };
    });
    return () => {
      cleanupFunctions.forEach((cleanupFunction) => cleanupFunction());
    };
  }, [committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);
}
function areEqual(arrayA, arrayB) {
  if (arrayA.length !== arrayB.length) {
    return false;
  }
  for (let index = 0; index < arrayA.length; index++) {
    if (arrayA[index] !== arrayB[index]) {
      return false;
    }
  }
  return true;
}
function isKeyDown(event) {
  return event.type === "keydown";
}
function isMouseEvent(event) {
  return event.type.startsWith("mouse");
}
function isTouchEvent(event) {
  return event.type.startsWith("touch");
}
function getResizeEventCursorPosition(direction, event) {
  const isHorizontal = direction === "horizontal";
  if (isMouseEvent(event)) {
    return isHorizontal ? event.clientX : event.clientY;
  } else if (isTouchEvent(event)) {
    const firstTouch = event.touches[0];
    return isHorizontal ? firstTouch.screenX : firstTouch.screenY;
  } else {
    throw Error(`Unsupported event type "${event.type}"`);
  }
}
function calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState) {
  const isHorizontal = direction === "horizontal";
  const handleElement = getResizeHandleElement(dragHandleId);
  const groupId = handleElement.getAttribute("data-panel-group-id");
  let {
    initialCursorPosition
  } = initialDragState;
  const cursorPosition = getResizeEventCursorPosition(direction, event);
  const groupElement = getPanelGroupElement(groupId);
  const groupRect = groupElement.getBoundingClientRect();
  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;
  const offsetPixels = cursorPosition - initialCursorPosition;
  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;
  return offsetPercentage;
}
function calculateDeltaPercentage(event, groupId, dragHandleId, direction, initialDragState, keyboardResizeByOptions) {
  if (isKeyDown(event)) {
    const isHorizontal = direction === "horizontal";
    const groupElement = getPanelGroupElement(groupId);
    const rect = groupElement.getBoundingClientRect();
    const groupSizeInPixels = isHorizontal ? rect.width : rect.height;
    let delta = 0;
    if (event.shiftKey) {
      delta = 100;
    } else if (keyboardResizeByOptions.percentage != null) {
      delta = keyboardResizeByOptions.percentage;
    } else if (keyboardResizeByOptions.pixels != null) {
      delta = keyboardResizeByOptions.pixels / groupSizeInPixels;
    } else {
      delta = 10;
    }
    let movement = 0;
    switch (event.key) {
      case "ArrowDown":
        movement = isHorizontal ? 0 : delta;
        break;
      case "ArrowLeft":
        movement = isHorizontal ? -delta : 0;
        break;
      case "ArrowRight":
        movement = isHorizontal ? delta : 0;
        break;
      case "ArrowUp":
        movement = isHorizontal ? 0 : -delta;
        break;
      case "End":
        movement = 100;
        break;
      case "Home":
        movement = -100;
        break;
    }
    return movement;
  } else {
    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState);
  }
}
function calculateUnsafeDefaultLayout({
  groupSizePixels,
  panelDataArray
}) {
  const layout = Array(panelDataArray.length);
  const panelDataConstraints = panelDataArray.map((panelData) => panelData.constraints);
  let numPanelsWithSizes = 0;
  let remainingSize = 100;
  for (let index = 0; index < panelDataArray.length; index++) {
    const {
      defaultSizePercentage
    } = computePercentagePanelConstraints(panelDataConstraints, index, groupSizePixels);
    if (defaultSizePercentage != null) {
      numPanelsWithSizes++;
      layout[index] = defaultSizePercentage;
      remainingSize -= defaultSizePercentage;
    }
  }
  for (let index = 0; index < panelDataArray.length; index++) {
    const {
      defaultSizePercentage
    } = computePercentagePanelConstraints(panelDataConstraints, index, groupSizePixels);
    if (defaultSizePercentage != null) {
      continue;
    }
    const numRemainingPanels = panelDataArray.length - numPanelsWithSizes;
    const size = remainingSize / numRemainingPanels;
    numPanelsWithSizes++;
    layout[index] = size;
    remainingSize -= size;
  }
  return layout;
}
function convertPercentageToPixels(percentage, groupSizePixels) {
  return percentage / 100 * groupSizePixels;
}
function callPanelCallbacks(groupId, panelsArray, layout, panelIdToLastNotifiedMixedSizesMap) {
  const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);
  layout.forEach((sizePercentage, index) => {
    const panelData = panelsArray[index];
    if (!panelData) {
      return;
    }
    const {
      callbacks,
      constraints,
      id: panelId
    } = panelData;
    const {
      collapsible
    } = constraints;
    const mixedSizes = {
      sizePercentage,
      sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
    };
    const lastNotifiedMixedSizes = panelIdToLastNotifiedMixedSizesMap[panelId];
    if (lastNotifiedMixedSizes == null || mixedSizes.sizePercentage !== lastNotifiedMixedSizes.sizePercentage || mixedSizes.sizePixels !== lastNotifiedMixedSizes.sizePixels) {
      panelIdToLastNotifiedMixedSizesMap[panelId] = mixedSizes;
      const {
        onCollapse,
        onExpand,
        onResize
      } = callbacks;
      if (onResize) {
        onResize(mixedSizes, lastNotifiedMixedSizes);
      }
      if (collapsible && (onCollapse || onExpand)) {
        var _getPercentageSizeFro;
        const collapsedSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({
          sizePercentage: constraints.collapsedSizePercentage,
          sizePixels: constraints.collapsedSizePixels
        }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;
        const size = getPercentageSizeFromMixedSizes(mixedSizes, groupSizePixels);
        if (onExpand && (lastNotifiedMixedSizes == null || lastNotifiedMixedSizes.sizePercentage === collapsedSize) && size !== collapsedSize) {
          onExpand();
        }
        if (onCollapse && (lastNotifiedMixedSizes == null || lastNotifiedMixedSizes.sizePercentage !== collapsedSize) && size === collapsedSize) {
          onCollapse();
        }
      }
    }
  });
}
function compareLayouts(a, b) {
  if (a.length !== b.length) {
    return false;
  } else {
    for (let index = 0; index < a.length; index++) {
      if (a[index] != b[index]) {
        return false;
      }
    }
  }
  return true;
}
function computePanelFlexBoxStyle({
  dragState,
  layout,
  panelData,
  panelIndex,
  precision = 3
}) {
  const size = layout[panelIndex];
  let flexGrow;
  if (panelData.length === 1) {
    flexGrow = "1";
  } else if (size == null) {
    flexGrow = "1";
  } else {
    flexGrow = size.toPrecision(precision);
  }
  return {
    flexBasis: 0,
    flexGrow,
    flexShrink: 1,
    // Without this, Panel sizes may be unintentionally overridden by their content
    overflow: "hidden",
    // Disable pointer events inside of a panel during resize
    // This avoid edge cases like nested iframes
    pointerEvents: dragState !== null ? "none" : void 0
  };
}
var currentState = null;
var element = null;
function getCursorStyle(state) {
  switch (state) {
    case "horizontal":
      return "ew-resize";
    case "horizontal-max":
      return "w-resize";
    case "horizontal-min":
      return "e-resize";
    case "vertical":
      return "ns-resize";
    case "vertical-max":
      return "n-resize";
    case "vertical-min":
      return "s-resize";
  }
}
function resetGlobalCursorStyle() {
  if (element !== null) {
    document.head.removeChild(element);
    currentState = null;
    element = null;
  }
}
function setGlobalCursorStyle(state) {
  if (currentState === state) {
    return;
  }
  currentState = state;
  const style = getCursorStyle(state);
  if (element === null) {
    element = document.createElement("style");
    document.head.appendChild(element);
  }
  element.innerHTML = `*{cursor: ${style}!important;}`;
}
function debounce(callback, durationMs = 10) {
  let timeoutId = null;
  let callable = (...args) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      callback(...args);
    }, durationMs);
  };
  return callable;
}
function getPanelElementsForGroup(groupId) {
  return Array.from(document.querySelectorAll(`[data-panel][data-panel-group-id="${groupId}"]`));
}
function initializeDefaultStorage(storageObject) {
  try {
    if (typeof localStorage !== "undefined") {
      storageObject.getItem = (name) => {
        return localStorage.getItem(name);
      };
      storageObject.setItem = (name, value) => {
        localStorage.setItem(name, value);
      };
    } else {
      throw new Error("localStorage not supported in this environment");
    }
  } catch (error) {
    console.error(error);
    storageObject.getItem = () => null;
    storageObject.setItem = () => {
    };
  }
}
function getSerializationKey(panels) {
  return panels.map((panel) => {
    const {
      constraints,
      id,
      idIsFromProps,
      order
    } = panel;
    if (idIsFromProps) {
      return id;
    } else {
      return `${order}:${JSON.stringify(constraints)}`;
    }
  }).sort((a, b) => a.localeCompare(b)).join(",");
}
function loadSerializedPanelGroupState(autoSaveId, storage) {
  try {
    const serialized = storage.getItem(`PanelGroup:sizes:${autoSaveId}`);
    if (serialized) {
      const parsed = JSON.parse(serialized);
      if (typeof parsed === "object" && parsed != null) {
        return parsed;
      }
    }
  } catch (error) {
  }
  return null;
}
function loadPanelLayout(autoSaveId, panels, storage) {
  const state = loadSerializedPanelGroupState(autoSaveId, storage);
  if (state) {
    var _state$key;
    const key = getSerializationKey(panels);
    return (_state$key = state[key]) !== null && _state$key !== void 0 ? _state$key : null;
  }
  return null;
}
function savePanelGroupLayout(autoSaveId, panels, sizes, storage) {
  const key = getSerializationKey(panels);
  const state = loadSerializedPanelGroupState(autoSaveId, storage) || {};
  state[key] = sizes;
  try {
    storage.setItem(`PanelGroup:sizes:${autoSaveId}`, JSON.stringify(state));
  } catch (error) {
    console.error(error);
  }
}
function shouldMonitorPixelBasedConstraints(constraints) {
  return constraints.some((constraints2) => {
    return constraints2.collapsedSizePixels !== void 0 || constraints2.maxSizePixels !== void 0 || constraints2.minSizePixels !== void 0;
  });
}
function validatePanelConstraints({
  groupSizePixels,
  panelConstraints,
  panelId,
  panelIndex
}) {
  {
    const warnings = [];
    {
      const {
        collapsedSizePercentage,
        collapsedSizePixels,
        defaultSizePercentage,
        defaultSizePixels,
        maxSizePercentage,
        maxSizePixels,
        minSizePercentage,
        minSizePixels
      } = panelConstraints[panelIndex];
      const conflictingUnits = [];
      if (collapsedSizePercentage != null && collapsedSizePixels != null) {
        conflictingUnits.push("collapsed size");
      }
      if (defaultSizePercentage != null && defaultSizePixels != null) {
        conflictingUnits.push("default size");
      }
      if (maxSizePercentage != null && maxSizePixels != null) {
        conflictingUnits.push("max size");
      }
      if (minSizePercentage != null && minSizePixels != null) {
        conflictingUnits.push("min size");
      }
      if (conflictingUnits.length > 0) {
        warnings.push(`should not specify both percentage and pixel units for: ${conflictingUnits.join(", ")}`);
      }
    }
    {
      const {
        collapsedSizePercentage,
        defaultSizePercentage,
        maxSizePercentage,
        minSizePercentage
      } = computePercentagePanelConstraints(panelConstraints, panelIndex, groupSizePixels);
      if (minSizePercentage > maxSizePercentage) {
        warnings.push(`min size (${minSizePercentage}%) should not be greater than max size (${maxSizePercentage}%)`);
      }
      if (defaultSizePercentage != null) {
        if (defaultSizePercentage < 0) {
          warnings.push("default size should not be less than 0");
        } else if (defaultSizePercentage < minSizePercentage) {
          warnings.push("default size should not be less than min size");
        }
        if (defaultSizePercentage > 100) {
          warnings.push("default size should not be greater than 100");
        } else if (defaultSizePercentage > maxSizePercentage) {
          warnings.push("default size should not be greater than max size");
        }
      }
      if (collapsedSizePercentage > minSizePercentage) {
        warnings.push("collapsed size should not be greater than min size");
      }
    }
    if (warnings.length > 0) {
      const name = panelId != null ? `Panel "${panelId}"` : "Panel";
      console.warn(`${name} has an invalid configuration:

${warnings.join("\n")}`);
      return false;
    }
  }
  return true;
}
function validatePanelGroupLayout({
  groupSizePixels,
  layout: prevLayout,
  panelConstraints
}) {
  const nextLayout = [...prevLayout];
  if (nextLayout.length !== panelConstraints.length) {
    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map((size) => `${size}%`).join(", ")}`);
  } else if (!fuzzyNumbersEqual(nextLayout.reduce((accumulated, current) => accumulated + current, 0), 100)) {
    {
      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map((size) => `${size}%`).join(", ")}`);
    }
  }
  let remainingSize = 0;
  for (let index = 0; index < panelConstraints.length; index++) {
    const unsafeSize = nextLayout[index];
    const safeSize = resizePanel({
      groupSizePixels,
      panelConstraints,
      panelIndex: index,
      size: unsafeSize
    });
    if (unsafeSize != safeSize) {
      remainingSize += unsafeSize - safeSize;
      nextLayout[index] = safeSize;
    }
  }
  if (!fuzzyNumbersEqual(remainingSize, 0)) {
    for (let index = 0; index < panelConstraints.length; index++) {
      const prevSize = nextLayout[index];
      const unsafeSize = prevSize + remainingSize;
      const safeSize = resizePanel({
        groupSizePixels,
        panelConstraints,
        panelIndex: index,
        size: unsafeSize
      });
      if (prevSize !== safeSize) {
        remainingSize -= safeSize - prevSize;
        nextLayout[index] = safeSize;
        if (fuzzyNumbersEqual(remainingSize, 0)) {
          break;
        }
      }
    }
  }
  return nextLayout;
}
var LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;
var defaultStorage = {
  getItem: (name) => {
    initializeDefaultStorage(defaultStorage);
    return defaultStorage.getItem(name);
  },
  setItem: (name, value) => {
    initializeDefaultStorage(defaultStorage);
    defaultStorage.setItem(name, value);
  }
};
var debounceMap = {};
function PanelGroupWithForwardedRef({
  autoSaveId = null,
  children,
  className: classNameFromProps = "",
  dataAttributes,
  direction,
  forwardedRef,
  id: idFromProps,
  onLayout = null,
  keyboardResizeByPercentage = null,
  keyboardResizeByPixels = null,
  storage = defaultStorage,
  style: styleFromProps,
  tagName: Type = "div"
}) {
  const groupId = useUniqueId(idFromProps);
  const [dragState, setDragState] = useState(null);
  const [layout, setLayout] = useState([]);
  const panelIdToLastNotifiedMixedSizesMapRef = useRef({});
  const panelSizeBeforeCollapseRef = useRef(/* @__PURE__ */ new Map());
  const prevDeltaRef = useRef(0);
  const committedValuesRef = useRef({
    autoSaveId,
    direction,
    dragState,
    id: groupId,
    keyboardResizeByPercentage,
    keyboardResizeByPixels,
    onLayout,
    storage
  });
  const eagerValuesRef = useRef({
    layout,
    panelDataArray: []
  });
  const devWarningsRef = useRef({
    didLogIdAndOrderWarning: false,
    didLogPanelConstraintsWarning: false,
    prevPanelIds: []
  });
  useImperativeHandle(forwardedRef, () => ({
    getId: () => committedValuesRef.current.id,
    getLayout: () => {
      const {
        id: groupId2
      } = committedValuesRef.current;
      const {
        layout: layout2
      } = eagerValuesRef.current;
      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId2);
      return layout2.map((sizePercentage) => {
        return {
          sizePercentage,
          sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
        };
      });
    },
    setLayout: (mixedSizes) => {
      const {
        id: groupId2,
        onLayout: onLayout2
      } = committedValuesRef.current;
      const {
        layout: prevLayout,
        panelDataArray
      } = eagerValuesRef.current;
      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId2);
      const unsafeLayout = mixedSizes.map((mixedSize) => getPercentageSizeFromMixedSizes(mixedSize, groupSizePixels));
      const safeLayout = validatePanelGroupLayout({
        groupSizePixels,
        layout: unsafeLayout,
        panelConstraints: panelDataArray.map((panelData) => panelData.constraints)
      });
      if (!areEqual(prevLayout, safeLayout)) {
        setLayout(safeLayout);
        eagerValuesRef.current.layout = safeLayout;
        if (onLayout2) {
          onLayout2(safeLayout.map((sizePercentage) => ({
            sizePercentage,
            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
          })));
        }
        callPanelCallbacks(groupId2, panelDataArray, safeLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
      }
    }
  }), []);
  useIsomorphicLayoutEffect(() => {
    committedValuesRef.current.autoSaveId = autoSaveId;
    committedValuesRef.current.direction = direction;
    committedValuesRef.current.dragState = dragState;
    committedValuesRef.current.id = groupId;
    committedValuesRef.current.onLayout = onLayout;
    committedValuesRef.current.storage = storage;
  });
  useWindowSplitterPanelGroupBehavior({
    committedValuesRef,
    eagerValuesRef,
    groupId,
    layout,
    panelDataArray: eagerValuesRef.current.panelDataArray,
    setLayout
  });
  useEffect(() => {
    const {
      panelDataArray
    } = eagerValuesRef.current;
    if (autoSaveId) {
      if (layout.length === 0 || layout.length !== panelDataArray.length) {
        return;
      }
      if (!debounceMap[autoSaveId]) {
        debounceMap[autoSaveId] = debounce(savePanelGroupLayout, LOCAL_STORAGE_DEBOUNCE_INTERVAL);
      }
      debounceMap[autoSaveId](autoSaveId, panelDataArray, layout, storage);
    }
  }, [autoSaveId, layout, storage]);
  useIsomorphicLayoutEffect(() => {
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    const constraints = panelDataArray.map(({
      constraints: constraints2
    }) => constraints2);
    if (!shouldMonitorPixelBasedConstraints(constraints)) {
      return;
    }
    if (typeof ResizeObserver === "undefined") {
      console.warn(`WARNING: Pixel based constraints require ResizeObserver but it is not supported by the current browser.`);
    } else {
      const resizeObserver = new ResizeObserver(() => {
        const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);
        const {
          onLayout: onLayout2
        } = committedValuesRef.current;
        const nextLayout = validatePanelGroupLayout({
          groupSizePixels,
          layout: prevLayout,
          panelConstraints: panelDataArray.map((panelData) => panelData.constraints)
        });
        if (!areEqual(prevLayout, nextLayout)) {
          setLayout(nextLayout);
          eagerValuesRef.current.layout = nextLayout;
          if (onLayout2) {
            onLayout2(nextLayout.map((sizePercentage) => ({
              sizePercentage,
              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
            })));
          }
          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
        }
      });
      resizeObserver.observe(getPanelGroupElement(groupId));
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [groupId]);
  useEffect(() => {
    {
      const {
        panelDataArray
      } = eagerValuesRef.current;
      const {
        didLogIdAndOrderWarning,
        didLogPanelConstraintsWarning,
        prevPanelIds
      } = devWarningsRef.current;
      if (!didLogIdAndOrderWarning) {
        const panelIds = panelDataArray.map(({
          id
        }) => id);
        devWarningsRef.current.prevPanelIds = panelIds;
        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);
        if (panelsHaveChanged) {
          if (panelDataArray.find(({
            idIsFromProps,
            order
          }) => !idIsFromProps || order == null)) {
            devWarningsRef.current.didLogIdAndOrderWarning = true;
            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);
          }
        }
      }
      if (!didLogPanelConstraintsWarning) {
        const panelConstraints = panelDataArray.map((panelData) => panelData.constraints);
        const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);
        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {
          const isValid = validatePanelConstraints({
            groupSizePixels,
            panelConstraints,
            panelId: panelDataArray[panelIndex].id,
            panelIndex
          });
          if (!isValid) {
            devWarningsRef.current.didLogPanelConstraintsWarning = true;
            break;
          }
        }
      }
    }
  });
  const collapsePanel = useCallback((panelData) => {
    const {
      onLayout: onLayout2
    } = committedValuesRef.current;
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    if (panelData.constraints.collapsible) {
      const panelConstraintsArray = panelDataArray.map((panelData2) => panelData2.constraints);
      const {
        collapsedSizePercentage,
        panelSizePercentage,
        pivotIndices,
        groupSizePixels
      } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);
      if (panelSizePercentage !== collapsedSizePercentage) {
        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSizePercentage);
        const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;
        const delta = isLastPanel ? panelSizePercentage - collapsedSizePercentage : collapsedSizePercentage - panelSizePercentage;
        const nextLayout = adjustLayoutByDelta({
          delta,
          groupSizePixels,
          layout: prevLayout,
          panelConstraints: panelConstraintsArray,
          pivotIndices,
          trigger: "imperative-api"
        });
        if (!compareLayouts(prevLayout, nextLayout)) {
          setLayout(nextLayout);
          eagerValuesRef.current.layout = nextLayout;
          if (onLayout2) {
            onLayout2(nextLayout.map((sizePercentage) => ({
              sizePercentage,
              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
            })));
          }
          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
        }
      }
    }
  }, [groupId]);
  const expandPanel = useCallback((panelData) => {
    const {
      onLayout: onLayout2
    } = committedValuesRef.current;
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    if (panelData.constraints.collapsible) {
      const panelConstraintsArray = panelDataArray.map((panelData2) => panelData2.constraints);
      const {
        collapsedSizePercentage,
        panelSizePercentage,
        minSizePercentage,
        pivotIndices,
        groupSizePixels
      } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);
      if (panelSizePercentage === collapsedSizePercentage) {
        const prevPanelSizePercentage = panelSizeBeforeCollapseRef.current.get(panelData.id);
        const baseSizePercentage = prevPanelSizePercentage != null && prevPanelSizePercentage >= minSizePercentage ? prevPanelSizePercentage : minSizePercentage;
        const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;
        const delta = isLastPanel ? panelSizePercentage - baseSizePercentage : baseSizePercentage - panelSizePercentage;
        const nextLayout = adjustLayoutByDelta({
          delta,
          groupSizePixels,
          layout: prevLayout,
          panelConstraints: panelConstraintsArray,
          pivotIndices,
          trigger: "imperative-api"
        });
        if (!compareLayouts(prevLayout, nextLayout)) {
          setLayout(nextLayout);
          eagerValuesRef.current.layout = nextLayout;
          if (onLayout2) {
            onLayout2(nextLayout.map((sizePercentage) => ({
              sizePercentage,
              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
            })));
          }
          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
        }
      }
    }
  }, [groupId]);
  const getPanelSize = useCallback((panelData) => {
    const {
      layout: layout2,
      panelDataArray
    } = eagerValuesRef.current;
    const {
      panelSizePercentage,
      panelSizePixels
    } = panelDataHelper(groupId, panelDataArray, panelData, layout2);
    return {
      sizePercentage: panelSizePercentage,
      sizePixels: panelSizePixels
    };
  }, [groupId]);
  const getPanelStyle = useCallback((panelData) => {
    const {
      panelDataArray
    } = eagerValuesRef.current;
    const panelIndex = panelDataArray.indexOf(panelData);
    return computePanelFlexBoxStyle({
      dragState,
      layout,
      panelData: panelDataArray,
      panelIndex
    });
  }, [dragState, layout]);
  const isPanelCollapsed = useCallback((panelData) => {
    const {
      layout: layout2,
      panelDataArray
    } = eagerValuesRef.current;
    const {
      collapsedSizePercentage,
      collapsible,
      panelSizePercentage
    } = panelDataHelper(groupId, panelDataArray, panelData, layout2);
    return collapsible === true && panelSizePercentage === collapsedSizePercentage;
  }, [groupId]);
  const isPanelExpanded = useCallback((panelData) => {
    const {
      layout: layout2,
      panelDataArray
    } = eagerValuesRef.current;
    const {
      collapsedSizePercentage,
      collapsible,
      panelSizePercentage
    } = panelDataHelper(groupId, panelDataArray, panelData, layout2);
    return !collapsible || panelSizePercentage > collapsedSizePercentage;
  }, [groupId]);
  const registerPanel = useCallback((panelData) => {
    const {
      autoSaveId: autoSaveId2,
      id: groupId2,
      onLayout: onLayout2,
      storage: storage2
    } = committedValuesRef.current;
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    panelDataArray.push(panelData);
    panelDataArray.sort((panelA, panelB) => {
      const orderA = panelA.order;
      const orderB = panelB.order;
      if (orderA == null && orderB == null) {
        return 0;
      } else if (orderA == null) {
        return -1;
      } else if (orderB == null) {
        return 1;
      } else {
        return orderA - orderB;
      }
    });
    const panelElements = getPanelElementsForGroup(groupId2);
    if (panelElements.length !== panelDataArray.length) {
      return;
    }
    let unsafeLayout = null;
    if (autoSaveId2) {
      unsafeLayout = loadPanelLayout(autoSaveId2, panelDataArray, storage2);
    }
    const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId2);
    if (groupSizePixels <= 0) {
      if (shouldMonitorPixelBasedConstraints(panelDataArray.map(({
        constraints
      }) => constraints))) {
        return;
      }
    }
    if (unsafeLayout == null) {
      unsafeLayout = calculateUnsafeDefaultLayout({
        groupSizePixels,
        panelDataArray
      });
    }
    const nextLayout = validatePanelGroupLayout({
      groupSizePixels,
      layout: unsafeLayout,
      panelConstraints: panelDataArray.map((panelData2) => panelData2.constraints)
    });
    setLayout(nextLayout);
    eagerValuesRef.current.layout = nextLayout;
    if (!areEqual(prevLayout, nextLayout)) {
      if (onLayout2) {
        onLayout2(nextLayout.map((sizePercentage) => ({
          sizePercentage,
          sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
        })));
      }
      callPanelCallbacks(groupId2, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
    }
  }, []);
  const registerResizeHandle = useCallback((dragHandleId) => {
    return function resizeHandler(event) {
      event.preventDefault();
      const {
        direction: direction2,
        dragState: dragState2,
        id: groupId2,
        keyboardResizeByPercentage: keyboardResizeByPercentage2,
        keyboardResizeByPixels: keyboardResizeByPixels2,
        onLayout: onLayout2
      } = committedValuesRef.current;
      const {
        layout: prevLayout,
        panelDataArray
      } = eagerValuesRef.current;
      const {
        initialLayout
      } = dragState2 !== null && dragState2 !== void 0 ? dragState2 : {};
      const pivotIndices = determinePivotIndices(groupId2, dragHandleId);
      let delta = calculateDeltaPercentage(event, groupId2, dragHandleId, direction2, dragState2, {
        percentage: keyboardResizeByPercentage2,
        pixels: keyboardResizeByPixels2
      });
      if (delta === 0) {
        return;
      }
      const isHorizontal = direction2 === "horizontal";
      if (document.dir === "rtl" && isHorizontal) {
        delta = -delta;
      }
      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId2);
      const panelConstraints = panelDataArray.map((panelData) => panelData.constraints);
      const nextLayout = adjustLayoutByDelta({
        delta,
        groupSizePixels,
        layout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,
        panelConstraints,
        pivotIndices,
        trigger: isKeyDown(event) ? "keyboard" : "mouse-or-touch"
      });
      const layoutChanged = !compareLayouts(prevLayout, nextLayout);
      if (isMouseEvent(event) || isTouchEvent(event)) {
        if (prevDeltaRef.current != delta) {
          prevDeltaRef.current = delta;
          if (!layoutChanged) {
            if (isHorizontal) {
              setGlobalCursorStyle(delta < 0 ? "horizontal-min" : "horizontal-max");
            } else {
              setGlobalCursorStyle(delta < 0 ? "vertical-min" : "vertical-max");
            }
          } else {
            setGlobalCursorStyle(isHorizontal ? "horizontal" : "vertical");
          }
        }
      }
      if (layoutChanged) {
        setLayout(nextLayout);
        eagerValuesRef.current.layout = nextLayout;
        if (onLayout2) {
          onLayout2(nextLayout.map((sizePercentage) => ({
            sizePercentage,
            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
          })));
        }
        callPanelCallbacks(groupId2, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
      }
    };
  }, []);
  const resizePanel2 = useCallback((panelData, mixedSizes) => {
    const {
      onLayout: onLayout2
    } = committedValuesRef.current;
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    const panelConstraintsArray = panelDataArray.map((panelData2) => panelData2.constraints);
    const {
      groupSizePixels,
      panelSizePercentage,
      pivotIndices
    } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);
    const sizePercentage = getPercentageSizeFromMixedSizes(mixedSizes, groupSizePixels);
    const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;
    const delta = isLastPanel ? panelSizePercentage - sizePercentage : sizePercentage - panelSizePercentage;
    const nextLayout = adjustLayoutByDelta({
      delta,
      groupSizePixels,
      layout: prevLayout,
      panelConstraints: panelConstraintsArray,
      pivotIndices,
      trigger: "imperative-api"
    });
    if (!compareLayouts(prevLayout, nextLayout)) {
      setLayout(nextLayout);
      eagerValuesRef.current.layout = nextLayout;
      if (onLayout2) {
        onLayout2(nextLayout.map((sizePercentage2) => ({
          sizePercentage: sizePercentage2,
          sizePixels: convertPercentageToPixels(sizePercentage2, groupSizePixels)
        })));
      }
      callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
    }
  }, [groupId]);
  const startDragging = useCallback((dragHandleId, event) => {
    const {
      direction: direction2
    } = committedValuesRef.current;
    const {
      layout: layout2
    } = eagerValuesRef.current;
    const handleElement = getResizeHandleElement(dragHandleId);
    const initialCursorPosition = getResizeEventCursorPosition(direction2, event);
    setDragState({
      dragHandleId,
      dragHandleRect: handleElement.getBoundingClientRect(),
      initialCursorPosition,
      initialLayout: layout2
    });
  }, []);
  const stopDragging = useCallback(() => {
    resetGlobalCursorStyle();
    setDragState(null);
  }, []);
  const unregisterPanelRef = useRef({
    pendingPanelIds: /* @__PURE__ */ new Set(),
    timeout: null
  });
  const unregisterPanel = useCallback((panelData) => {
    const {
      id: groupId2,
      onLayout: onLayout2
    } = committedValuesRef.current;
    const {
      layout: prevLayout,
      panelDataArray
    } = eagerValuesRef.current;
    const index = panelDataArray.indexOf(panelData);
    if (index >= 0) {
      panelDataArray.splice(index, 1);
      unregisterPanelRef.current.pendingPanelIds.add(panelData.id);
    }
    if (unregisterPanelRef.current.timeout != null) {
      clearTimeout(unregisterPanelRef.current.timeout);
    }
    unregisterPanelRef.current.timeout = setTimeout(() => {
      const {
        pendingPanelIds
      } = unregisterPanelRef.current;
      const map = panelIdToLastNotifiedMixedSizesMapRef.current;
      let unmountDueToStrictMode = false;
      pendingPanelIds.forEach((panelId) => {
        pendingPanelIds.delete(panelId);
        if (panelDataArray.find(({
          id
        }) => id === panelId) == null) {
          unmountDueToStrictMode = true;
          delete map[panelData.id];
        }
      });
      if (!unmountDueToStrictMode) {
        return;
      }
      if (panelDataArray.length === 0) {
        return;
      }
      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId2);
      let unsafeLayout = calculateUnsafeDefaultLayout({
        groupSizePixels,
        panelDataArray
      });
      const nextLayout = validatePanelGroupLayout({
        groupSizePixels,
        layout: unsafeLayout,
        panelConstraints: panelDataArray.map((panelData2) => panelData2.constraints)
      });
      if (!areEqual(prevLayout, nextLayout)) {
        setLayout(nextLayout);
        eagerValuesRef.current.layout = nextLayout;
        if (onLayout2) {
          onLayout2(nextLayout.map((sizePercentage) => ({
            sizePercentage,
            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)
          })));
        }
        callPanelCallbacks(groupId2, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);
      }
    }, 0);
  }, []);
  const context = useMemo(() => ({
    collapsePanel,
    direction,
    dragState,
    expandPanel,
    getPanelSize,
    getPanelStyle,
    groupId,
    isPanelCollapsed,
    isPanelExpanded,
    registerPanel,
    registerResizeHandle,
    resizePanel: resizePanel2,
    startDragging,
    stopDragging,
    unregisterPanel
  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, registerPanel, registerResizeHandle, resizePanel2, startDragging, stopDragging, unregisterPanel]);
  const style = {
    display: "flex",
    flexDirection: direction === "horizontal" ? "row" : "column",
    height: "100%",
    overflow: "hidden",
    width: "100%"
  };
  return createElement(PanelGroupContext.Provider, {
    value: context
  }, createElement(Type, {
    children,
    className: classNameFromProps,
    style: {
      ...style,
      ...styleFromProps
    },
    ...dataAttributes,
    // CSS selectors
    "data-panel-group": "",
    "data-panel-group-direction": direction,
    "data-panel-group-id": groupId
  }));
}
var PanelGroup = forwardRef((props, ref) => createElement(PanelGroupWithForwardedRef, {
  ...props,
  forwardedRef: ref
}));
PanelGroupWithForwardedRef.displayName = "PanelGroup";
PanelGroup.displayName = "forwardRef(PanelGroup)";
function panelDataHelper(groupId, panelDataArray, panelData, layout) {
  const panelConstraintsArray = panelDataArray.map((panelData2) => panelData2.constraints);
  const panelIndex = panelDataArray.indexOf(panelData);
  const panelConstraints = panelConstraintsArray[panelIndex];
  const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);
  const percentagePanelConstraints = computePercentagePanelConstraints(panelConstraintsArray, panelIndex, groupSizePixels);
  const isLastPanel = panelIndex === panelDataArray.length - 1;
  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];
  const panelSizePercentage = layout[panelIndex];
  const panelSizePixels = convertPercentageToPixels(panelSizePercentage, groupSizePixels);
  return {
    ...percentagePanelConstraints,
    collapsible: panelConstraints.collapsible,
    panelSizePercentage,
    panelSizePixels,
    groupSizePixels,
    pivotIndices
  };
}
function useWindowSplitterResizeHandlerBehavior({
  disabled,
  handleId,
  resizeHandler
}) {
  useEffect(() => {
    if (disabled || resizeHandler == null) {
      return;
    }
    const handleElement = getResizeHandleElement(handleId);
    if (handleElement == null) {
      return;
    }
    const onKeyDown = (event) => {
      if (event.defaultPrevented) {
        return;
      }
      switch (event.key) {
        case "ArrowDown":
        case "ArrowLeft":
        case "ArrowRight":
        case "ArrowUp":
        case "End":
        case "Home": {
          event.preventDefault();
          resizeHandler(event);
          break;
        }
        case "F6": {
          event.preventDefault();
          const groupId = handleElement.getAttribute("data-panel-group-id");
          const handles = getResizeHandleElementsForGroup(groupId);
          const index = getResizeHandleElementIndex(groupId, handleId);
          assert(index !== null);
          const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;
          const nextHandle = handles[nextIndex];
          nextHandle.focus();
          break;
        }
      }
    };
    handleElement.addEventListener("keydown", onKeyDown);
    return () => {
      handleElement.removeEventListener("keydown", onKeyDown);
    };
  }, [disabled, handleId, resizeHandler]);
}
function PanelResizeHandle({
  children = null,
  className: classNameFromProps = "",
  dataAttributes,
  disabled = false,
  id: idFromProps = null,
  onDragging,
  style: styleFromProps = {},
  tagName: Type = "div"
}) {
  const divElementRef = useRef(null);
  const callbacksRef = useRef({
    onDragging
  });
  useEffect(() => {
    callbacksRef.current.onDragging = onDragging;
  });
  const panelGroupContext = useContext(PanelGroupContext);
  if (panelGroupContext === null) {
    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);
  }
  const {
    direction,
    dragState,
    groupId,
    registerResizeHandle,
    startDragging,
    stopDragging
  } = panelGroupContext;
  const resizeHandleId = useUniqueId(idFromProps);
  const isDragging = (dragState === null || dragState === void 0 ? void 0 : dragState.dragHandleId) === resizeHandleId;
  const [isFocused, setIsFocused] = useState(false);
  const [resizeHandler, setResizeHandler] = useState(null);
  const stopDraggingAndBlur = useCallback(() => {
    const div = divElementRef.current;
    div.blur();
    stopDragging();
    const {
      onDragging: onDragging2
    } = callbacksRef.current;
    if (onDragging2) {
      onDragging2(false);
    }
  }, [stopDragging]);
  useEffect(() => {
    if (disabled) {
      setResizeHandler(null);
    } else {
      const resizeHandler2 = registerResizeHandle(resizeHandleId);
      setResizeHandler(() => resizeHandler2);
    }
  }, [disabled, resizeHandleId, registerResizeHandle]);
  useEffect(() => {
    if (disabled || resizeHandler == null || !isDragging) {
      return;
    }
    const onMove = (event) => {
      resizeHandler(event);
    };
    const onMouseLeave = (event) => {
      resizeHandler(event);
    };
    const divElement = divElementRef.current;
    const targetDocument = divElement.ownerDocument;
    targetDocument.body.addEventListener("contextmenu", stopDraggingAndBlur);
    targetDocument.body.addEventListener("mousemove", onMove);
    targetDocument.body.addEventListener("touchmove", onMove);
    targetDocument.body.addEventListener("mouseleave", onMouseLeave);
    window.addEventListener("mouseup", stopDraggingAndBlur);
    window.addEventListener("touchend", stopDraggingAndBlur);
    return () => {
      targetDocument.body.removeEventListener("contextmenu", stopDraggingAndBlur);
      targetDocument.body.removeEventListener("mousemove", onMove);
      targetDocument.body.removeEventListener("touchmove", onMove);
      targetDocument.body.removeEventListener("mouseleave", onMouseLeave);
      window.removeEventListener("mouseup", stopDraggingAndBlur);
      window.removeEventListener("touchend", stopDraggingAndBlur);
    };
  }, [direction, disabled, isDragging, resizeHandler, stopDraggingAndBlur]);
  useWindowSplitterResizeHandlerBehavior({
    disabled,
    handleId: resizeHandleId,
    resizeHandler
  });
  const style = {
    cursor: getCursorStyle(direction),
    touchAction: "none",
    userSelect: "none"
  };
  return createElement(Type, {
    children,
    className: classNameFromProps,
    onBlur: () => setIsFocused(false),
    onFocus: () => setIsFocused(true),
    onMouseDown: (event) => {
      startDragging(resizeHandleId, event.nativeEvent);
      const {
        onDragging: onDragging2
      } = callbacksRef.current;
      if (onDragging2) {
        onDragging2(true);
      }
    },
    onMouseUp: stopDraggingAndBlur,
    onTouchCancel: stopDraggingAndBlur,
    onTouchEnd: stopDraggingAndBlur,
    onTouchStart: (event) => {
      startDragging(resizeHandleId, event.nativeEvent);
      const {
        onDragging: onDragging2
      } = callbacksRef.current;
      if (onDragging2) {
        onDragging2(true);
      }
    },
    ref: divElementRef,
    role: "separator",
    style: {
      ...style,
      ...styleFromProps
    },
    tabIndex: 0,
    ...dataAttributes,
    // CSS selectors
    "data-panel-group-direction": direction,
    "data-panel-group-id": groupId,
    "data-resize-handle": "",
    "data-resize-handle-active": isDragging ? "pointer" : isFocused ? "keyboard" : void 0,
    "data-panel-resize-handle-enabled": !disabled,
    "data-panel-resize-handle-id": resizeHandleId
  });
}
PanelResizeHandle.displayName = "PanelResizeHandle";
export {
  Panel,
  PanelGroup,
  PanelResizeHandle
};
//# sourceMappingURL=react-resizable-panels.js.map
