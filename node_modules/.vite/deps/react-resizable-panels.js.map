{"version": 3, "sources": ["../../../lighthouse-lm/node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js"], "sourcesContent": ["import * as React from 'react';\n\n// This module exists to work around Webpack issue https://github.com/webpack/webpack/issues/14814\n\n// eslint-disable-next-line no-restricted-imports\n\nconst {\n  createElement,\n  createContext,\n  createRef,\n  forwardRef,\n  useCallback,\n  useContext,\n  useEffect,\n  useImperativeHandle,\n  useLayoutEffect,\n  useMemo,\n  useRef,\n  useState\n} = React;\n\n// `toString()` prevents bundlers from trying to `import { useId } from 'react'`\nconst useId = React[\"useId\".toString()];\n\nconst PanelGroupContext = createContext(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst useIsomorphicLayoutEffect = useLayoutEffect ;\n\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = useRef(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSizePercentage,\n  collapsedSizePixels,\n  collapsible,\n  dataAttributes,\n  defaultSizePercentage,\n  defaultSizePixels,\n  forwardedRef,\n  id: idFromProps,\n  maxSizePercentage,\n  maxSizePixels,\n  minSizePercentage,\n  minSizePixels,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\"\n}) {\n  const context = useContext(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = useRef({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSizePercentage,\n      collapsedSizePixels,\n      collapsible,\n      defaultSizePercentage,\n      defaultSizePixels,\n      maxSizePercentage,\n      maxSizePixels,\n      minSizePercentage,\n      minSizePixels\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = useRef({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) ;\n  }\n  useIsomorphicLayoutEffect(() => {\n    const {\n      callbacks,\n      constraints\n    } = panelDataRef.current;\n    panelDataRef.current.id = panelId;\n    panelDataRef.current.idIsFromProps = idFromProps !== undefined;\n    panelDataRef.current.order = order;\n    callbacks.onCollapse = onCollapse;\n    callbacks.onExpand = onExpand;\n    callbacks.onResize = onResize;\n    constraints.collapsedSizePercentage = collapsedSizePercentage;\n    constraints.collapsedSizePixels = collapsedSizePixels;\n    constraints.collapsible = collapsible;\n    constraints.defaultSizePercentage = defaultSizePercentage;\n    constraints.defaultSizePixels = defaultSizePixels;\n    constraints.maxSizePercentage = maxSizePercentage;\n    constraints.maxSizePixels = maxSizePixels;\n    constraints.minSizePercentage = minSizePercentage;\n    constraints.minSizePixels = minSizePixels;\n  });\n  useIsomorphicLayoutEffect(() => {\n    const panelData = panelDataRef.current;\n    registerPanel(panelData);\n    return () => {\n      unregisterPanel(panelData);\n    };\n  }, [order, panelId, registerPanel, unregisterPanel]);\n  useImperativeHandle(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: () => {\n      expandPanel(panelDataRef.current);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: mixedSizes => {\n      resizePanel(panelDataRef.current, mixedSizes);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current);\n  return createElement(Type, {\n    children,\n    className: classNameFromProps,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    ...dataAttributes,\n    // CSS selectors\n    \"data-panel\": \"\",\n    \"data-panel-id\": panelId,\n    \"data-panel-group-id\": groupId,\n    // e2e test attributes\n    \"data-panel-collapsible\": collapsible || undefined ,\n    \"data-panel-size\": parseFloat(\"\" + style.flexGrow).toFixed(1) \n  });\n}\nconst Panel = forwardRef((props, ref) => createElement(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nfunction convertPixelsToPercentage(pixels, groupSizePixels) {\n  return pixels / groupSizePixels * 100;\n}\n\nfunction convertPixelConstraintsToPercentages(panelConstraints, groupSizePixels) {\n  let {\n    collapsedSizePercentage = 0,\n    collapsedSizePixels,\n    defaultSizePercentage,\n    defaultSizePixels,\n    maxSizePercentage = 100,\n    maxSizePixels,\n    minSizePercentage = 0,\n    minSizePixels\n  } = panelConstraints;\n  const hasPixelConstraints = collapsedSizePixels != null || defaultSizePixels != null || minSizePixels != null || maxSizePixels != null;\n  if (hasPixelConstraints && groupSizePixels <= 0) {\n    console.warn(`WARNING: Invalid group size: ${groupSizePixels}px`);\n    return {\n      collapsedSizePercentage: 0,\n      defaultSizePercentage,\n      maxSizePercentage: 0,\n      minSizePercentage: 0\n    };\n  }\n  if (collapsedSizePixels != null) {\n    collapsedSizePercentage = convertPixelsToPercentage(collapsedSizePixels, groupSizePixels);\n  }\n  if (defaultSizePixels != null) {\n    defaultSizePercentage = convertPixelsToPercentage(defaultSizePixels, groupSizePixels);\n  }\n  if (minSizePixels != null) {\n    minSizePercentage = convertPixelsToPercentage(minSizePixels, groupSizePixels);\n  }\n  if (maxSizePixels != null) {\n    maxSizePercentage = convertPixelsToPercentage(maxSizePixels, groupSizePixels);\n  }\n  return {\n    collapsedSizePercentage,\n    defaultSizePercentage,\n    maxSizePercentage,\n    minSizePercentage\n  };\n}\n\nfunction computePercentagePanelConstraints(panelConstraintsArray, panelIndex, groupSizePixels) {\n  // All panel constraints, excluding the current one\n  let totalMinConstraints = 0;\n  let totalMaxConstraints = 0;\n  for (let index = 0; index < panelConstraintsArray.length; index++) {\n    if (index !== panelIndex) {\n      const {\n        collapsible\n      } = panelConstraintsArray[index];\n      const {\n        collapsedSizePercentage,\n        maxSizePercentage,\n        minSizePercentage\n      } = convertPixelConstraintsToPercentages(panelConstraintsArray[index], groupSizePixels);\n      totalMaxConstraints += maxSizePercentage;\n      totalMinConstraints += collapsible ? collapsedSizePercentage : minSizePercentage;\n    }\n  }\n  const {\n    collapsedSizePercentage,\n    defaultSizePercentage,\n    maxSizePercentage,\n    minSizePercentage\n  } = convertPixelConstraintsToPercentages(panelConstraintsArray[panelIndex], groupSizePixels);\n  return {\n    collapsedSizePercentage,\n    defaultSizePercentage,\n    maxSizePercentage: panelConstraintsArray.length > 1 ? Math.min(maxSizePercentage, 100 - totalMinConstraints) : maxSizePercentage,\n    minSizePercentage: panelConstraintsArray.length > 1 ? Math.max(minSizePercentage, 100 - totalMaxConstraints) : minSizePercentage\n  };\n}\n\nconst PRECISION = 10;\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  actual = parseFloat(actual.toFixed(fractionDigits));\n  expected = parseFloat(expected.toFixed(fractionDigits));\n  const delta = actual - expected;\n  if (delta === 0) {\n    return 0;\n  } else {\n    return delta > 0 ? 1 : -1;\n  }\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  groupSizePixels,\n  panelConstraints,\n  panelIndex,\n  size\n}) {\n  const hasPixelConstraints = panelConstraints.some(({\n    collapsedSizePixels,\n    defaultSizePixels,\n    minSizePixels,\n    maxSizePixels\n  }) => collapsedSizePixels != null || defaultSizePixels != null || minSizePixels != null || maxSizePixels != null);\n  if (hasPixelConstraints && groupSizePixels <= 0) {\n    console.warn(`WARNING: Invalid group size: ${groupSizePixels}px`);\n    return 0;\n  }\n  let {\n    collapsible\n  } = panelConstraints[panelIndex];\n  const {\n    collapsedSizePercentage,\n    maxSizePercentage,\n    minSizePercentage\n  } = computePercentagePanelConstraints(panelConstraints, panelIndex, groupSizePixels);\n  if (minSizePercentage != null) {\n    if (fuzzyCompareNumbers(size, minSizePercentage) < 0) {\n      if (collapsible) {\n        // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n        const halfwayPoint = (collapsedSizePercentage + minSizePercentage) / 2;\n        if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n          size = collapsedSizePercentage;\n        } else {\n          size = minSizePercentage;\n        }\n      } else {\n        size = minSizePercentage;\n      }\n    }\n  }\n  if (maxSizePercentage != null) {\n    size = Math.min(maxSizePercentage, size);\n  }\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  groupSizePixels,\n  layout: prevLayout,\n  panelConstraints,\n  pivotIndices,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return prevLayout;\n  }\n  const nextLayout = [...prevLayout];\n  let deltaApplied = 0;\n\n  //const DEBUG = [];\n  //DEBUG.push(`adjustLayoutByDelta() ${prevLayout.join(\", \")}`);\n  //DEBUG.push(`  delta: ${delta}`);\n  //DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  //DEBUG.push(`  trigger: ${trigger}`);\n  //DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? pivotIndices[1] : pivotIndices[0];\n        const constraints = panelConstraints[index];\n        //DEBUG.push(`edge case check 1: ${index}`);\n        //DEBUG.push(`  -> collapsible? ${constraints.collapsible}`);\n        if (constraints.collapsible) {\n          const prevSize = prevLayout[index];\n          const {\n            collapsedSizePercentage,\n            minSizePercentage\n          } = computePercentagePanelConstraints(panelConstraints, index, groupSizePixels);\n          if (fuzzyNumbersEqual(prevSize, collapsedSizePercentage)) {\n            const localDelta = minSizePercentage - prevSize;\n            //DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              //DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? pivotIndices[0] : pivotIndices[1];\n        const constraints = panelConstraints[index];\n        //DEBUG.push(`edge case check 2: ${index}`);\n        //DEBUG.push(`  -> collapsible? ${constraints.collapsible}`);\n        if (constraints.collapsible) {\n          const prevSize = prevLayout[index];\n          const {\n            collapsedSizePercentage,\n            minSizePercentage\n          } = computePercentagePanelConstraints(panelConstraints, index, groupSizePixels);\n          if (fuzzyNumbersEqual(prevSize, minSizePercentage)) {\n            const localDelta = prevSize - collapsedSizePercentage;\n            //DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              //DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    //DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? pivotIndices[1] : pivotIndices[0];\n    let maxAvailableDelta = 0;\n\n    //DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = prevLayout[index];\n      const maxSafeSize = resizePanel({\n        groupSizePixels,\n        panelConstraints,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      //DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraints.length) {\n        break;\n      }\n    }\n\n    //DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    //DEBUG.push(`  -> adjusted delta: ${delta}`);\n    //DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? pivotIndices[0] : pivotIndices[1];\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraints.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = prevLayout[index];\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        groupSizePixels,\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  //DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  //DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  //DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyNumbersEqual(deltaApplied, 0)) {\n    //console.log(DEBUG.join(\"\\n\"));\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? pivotIndices[1] : pivotIndices[0];\n    const unsafeSize = prevLayout[pivotIndex] + deltaApplied;\n    const safeSize = resizePanel({\n      groupSizePixels,\n      panelConstraints,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? pivotIndices[1] : pivotIndices[0];\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraints.length) {\n        const prevSize = nextLayout[index];\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          groupSizePixels,\n          panelConstraints,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  //DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  //DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  //DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  deltaApplied = 100 - totalSize;\n  //DEBUG.push(`total size: ${totalSize}`);\n  //DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  //console.log(DEBUG.join(\"\\n\"));\n\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    return prevLayout;\n  }\n  return nextLayout;\n}\n\nfunction assert(expectedCondition, message = \"Assertion failed!\") {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction getPercentageSizeFromMixedSizes({\n  sizePercentage,\n  sizePixels\n}, groupSizePixels) {\n  if (sizePercentage != null) {\n    return sizePercentage;\n  } else if (sizePixels != null) {\n    return convertPixelsToPercentage(sizePixels, groupSizePixels);\n  }\n  return undefined;\n}\n\nfunction calculateAriaValues({\n  groupSizePixels,\n  layout,\n  panelsArray,\n  pivotIndices\n}) {\n  let currentMinSize = 0;\n  let currentMaxSize = 100;\n  let totalMinSize = 0;\n  let totalMaxSize = 0;\n\n  // A panel's effective min/max sizes also need to account for other panel's sizes.\n  panelsArray.forEach((panelData, index) => {\n    var _getPercentageSizeFro, _getPercentageSizeFro2;\n    const {\n      constraints\n    } = panelData;\n    const {\n      maxSizePercentage,\n      maxSizePixels,\n      minSizePercentage,\n      minSizePixels\n    } = constraints;\n    const minSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({\n      sizePercentage: minSizePercentage,\n      sizePixels: minSizePixels\n    }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;\n    const maxSize = (_getPercentageSizeFro2 = getPercentageSizeFromMixedSizes({\n      sizePercentage: maxSizePercentage,\n      sizePixels: maxSizePixels\n    }, groupSizePixels)) !== null && _getPercentageSizeFro2 !== void 0 ? _getPercentageSizeFro2 : 100;\n    if (index === pivotIndices[0]) {\n      currentMinSize = minSize;\n      currentMaxSize = maxSize;\n    } else {\n      totalMinSize += minSize;\n      totalMaxSize += maxSize;\n    }\n  });\n  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);\n  const valueNow = layout[pivotIndices[0]];\n  return {\n    valueMax,\n    valueMin,\n    valueNow\n  };\n}\n\nfunction getResizeHandleElementsForGroup(groupId) {\n  return Array.from(document.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id) {\n  const handles = getResizeHandleElementsForGroup(groupId);\n  const index = handles.findIndex(handle => handle.getAttribute(\"data-panel-resize-handle-id\") === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction getPanelGroupElement(id) {\n  const element = document.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction calculateAvailablePanelSizeInPixels(groupId) {\n  const panelGroupElement = getPanelGroupElement(groupId);\n  if (panelGroupElement == null) {\n    return NaN;\n  }\n  const direction = panelGroupElement.getAttribute(\"data-panel-group-direction\");\n  const resizeHandles = getResizeHandleElementsForGroup(groupId);\n  if (direction === \"horizontal\") {\n    return panelGroupElement.offsetWidth - resizeHandles.reduce((accumulated, handle) => {\n      return accumulated + handle.offsetWidth;\n    }, 0);\n  } else {\n    return panelGroupElement.offsetHeight - resizeHandles.reduce((accumulated, handle) => {\n      return accumulated + handle.offsetHeight;\n    }, 0);\n  }\n}\n\nfunction getAvailableGroupSizePixels(groupId) {\n  const panelGroupElement = getPanelGroupElement(groupId);\n  if (panelGroupElement == null) {\n    return NaN;\n  }\n  const direction = panelGroupElement.getAttribute(\"data-panel-group-direction\");\n  const resizeHandles = getResizeHandleElementsForGroup(groupId);\n  if (direction === \"horizontal\") {\n    return panelGroupElement.offsetWidth - resizeHandles.reduce((accumulated, handle) => {\n      return accumulated + handle.offsetWidth;\n    }, 0);\n  } else {\n    return panelGroupElement.offsetHeight - resizeHandles.reduce((accumulated, handle) => {\n      return accumulated + handle.offsetHeight;\n    }, 0);\n  }\n}\n\nfunction getResizeHandleElement(id) {\n  const element = document.querySelector(`[data-panel-resize-handle-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId);\n  const handles = getResizeHandleElementsForGroup(groupId);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  setLayout\n}) {\n  const devWarningsRef = useRef({\n    didWarnAboutMissingResizeHandle: false\n  });\n  useIsomorphicLayoutEffect(() => {\n    const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n    const resizeHandleElements = getResizeHandleElementsForGroup(groupId);\n    for (let index = 0; index < panelDataArray.length - 1; index++) {\n      const {\n        valueMax,\n        valueMin,\n        valueNow\n      } = calculateAriaValues({\n        groupSizePixels,\n        layout,\n        panelsArray: panelDataArray,\n        pivotIndices: [index, index + 1]\n      });\n      const resizeHandleElement = resizeHandleElements[index];\n      if (resizeHandleElement == null) {\n        {\n          const {\n            didWarnAboutMissingResizeHandle\n          } = devWarningsRef.current;\n          if (!didWarnAboutMissingResizeHandle) {\n            devWarningsRef.current.didWarnAboutMissingResizeHandle = true;\n            console.warn(`WARNING: Missing resize handle for PanelGroup \"${groupId}\"`);\n          }\n        }\n      } else {\n        resizeHandleElement.setAttribute(\"aria-controls\", panelDataArray[index].id);\n        resizeHandleElement.setAttribute(\"aria-valuemax\", \"\" + Math.round(valueMax));\n        resizeHandleElement.setAttribute(\"aria-valuemin\", \"\" + Math.round(valueMin));\n        resizeHandleElement.setAttribute(\"aria-valuenow\", \"\" + Math.round(valueNow));\n      }\n    }\n    return () => {\n      resizeHandleElements.forEach((resizeHandleElement, index) => {\n        resizeHandleElement.removeAttribute(\"aria-controls\");\n        resizeHandleElement.removeAttribute(\"aria-valuemax\");\n        resizeHandleElement.removeAttribute(\"aria-valuemin\");\n        resizeHandleElement.removeAttribute(\"aria-valuenow\");\n      });\n    };\n  }, [groupId, layout, panelDataArray]);\n  useEffect(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const groupElement = getPanelGroupElement(groupId);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(\"data-panel-resize-handle-id\");\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                const size = layout[index];\n                if (size != null && panelData.constraints.collapsible) {\n                  var _getPercentageSizeFro, _getPercentageSizeFro2;\n                  const groupSizePixels = getAvailableGroupSizePixels(groupId);\n                  const collapsedSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({\n                    sizePercentage: panelData.constraints.collapsedSizePercentage,\n                    sizePixels: panelData.constraints.collapsedSizePixels\n                  }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;\n                  const minSize = (_getPercentageSizeFro2 = getPercentageSizeFromMixedSizes({\n                    sizePercentage: panelData.constraints.minSizePercentage,\n                    sizePixels: panelData.constraints.minSizePixels\n                  }, groupSizePixels)) !== null && _getPercentageSizeFro2 !== void 0 ? _getPercentageSizeFro2 : 0;\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    groupSizePixels,\n                    layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId),\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\nfunction isTouchEvent(event) {\n  return event.type.startsWith(\"touch\");\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  if (isMouseEvent(event)) {\n    return isHorizontal ? event.clientX : event.clientY;\n  } else if (isTouchEvent(event)) {\n    const firstTouch = event.touches[0];\n    return isHorizontal ? firstTouch.screenX : firstTouch.screenY;\n  } else {\n    throw Error(`Unsupported event type \"${event.type}\"`);\n  }\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId);\n  const groupId = handleElement.getAttribute(\"data-panel-group-id\");\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, groupId, dragHandleId, direction, initialDragState, keyboardResizeByOptions) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    const groupElement = getPanelGroupElement(groupId);\n    const rect = groupElement.getBoundingClientRect();\n    const groupSizeInPixels = isHorizontal ? rect.width : rect.height;\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeByOptions.percentage != null) {\n      delta = keyboardResizeByOptions.percentage;\n    } else if (keyboardResizeByOptions.pixels != null) {\n      delta = keyboardResizeByOptions.pixels / groupSizeInPixels;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState);\n  }\n}\n\nfunction calculateUnsafeDefaultLayout({\n  groupSizePixels,\n  panelDataArray\n}) {\n  const layout = Array(panelDataArray.length);\n  const panelDataConstraints = panelDataArray.map(panelData => panelData.constraints);\n  let numPanelsWithSizes = 0;\n  let remainingSize = 100;\n\n  // Distribute default sizes first\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const {\n      defaultSizePercentage\n    } = computePercentagePanelConstraints(panelDataConstraints, index, groupSizePixels);\n    if (defaultSizePercentage != null) {\n      numPanelsWithSizes++;\n      layout[index] = defaultSizePercentage;\n      remainingSize -= defaultSizePercentage;\n    }\n  }\n\n  // Remaining size should be distributed evenly between panels without default sizes\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const {\n      defaultSizePercentage\n    } = computePercentagePanelConstraints(panelDataConstraints, index, groupSizePixels);\n    if (defaultSizePercentage != null) {\n      continue;\n    }\n    const numRemainingPanels = panelDataArray.length - numPanelsWithSizes;\n    const size = remainingSize / numRemainingPanels;\n    numPanelsWithSizes++;\n    layout[index] = size;\n    remainingSize -= size;\n  }\n  return layout;\n}\n\nfunction convertPercentageToPixels(percentage, groupSizePixels) {\n  return percentage / 100 * groupSizePixels;\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(groupId, panelsArray, layout, panelIdToLastNotifiedMixedSizesMap) {\n  const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n  layout.forEach((sizePercentage, index) => {\n    const panelData = panelsArray[index];\n    if (!panelData) {\n      // Handle initial mount (when panels are registered too late to be in the panels array)\n      // The subsequent render+effects will handle the resize notification\n      return;\n    }\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsible\n    } = constraints;\n    const mixedSizes = {\n      sizePercentage,\n      sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n    };\n    const lastNotifiedMixedSizes = panelIdToLastNotifiedMixedSizesMap[panelId];\n    if (lastNotifiedMixedSizes == null || mixedSizes.sizePercentage !== lastNotifiedMixedSizes.sizePercentage || mixedSizes.sizePixels !== lastNotifiedMixedSizes.sizePixels) {\n      panelIdToLastNotifiedMixedSizesMap[panelId] = mixedSizes;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(mixedSizes, lastNotifiedMixedSizes);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        var _getPercentageSizeFro;\n        const collapsedSize = (_getPercentageSizeFro = getPercentageSizeFromMixedSizes({\n          sizePercentage: constraints.collapsedSizePercentage,\n          sizePixels: constraints.collapsedSizePixels\n        }, groupSizePixels)) !== null && _getPercentageSizeFro !== void 0 ? _getPercentageSizeFro : 0;\n        const size = getPercentageSizeFromMixedSizes(mixedSizes, groupSizePixels);\n        if (onExpand && (lastNotifiedMixedSizes == null || lastNotifiedMixedSizes.sizePercentage === collapsedSize) && size !== collapsedSize) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedMixedSizes == null || lastNotifiedMixedSizes.sizePercentage !== collapsedSize) && size === collapsedSize) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (panelData.length === 1) {\n    flexGrow = \"1\";\n  } else if (size == null) {\n    // Initial render (before panels have registered themselves)\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nlet currentState = null;\nlet element = null;\nfunction getCursorStyle(state) {\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"horizontal-max\":\n      return \"w-resize\";\n    case \"horizontal-min\":\n      return \"e-resize\";\n    case \"vertical\":\n      return \"ns-resize\";\n    case \"vertical-max\":\n      return \"n-resize\";\n    case \"vertical-min\":\n      return \"s-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (element !== null) {\n    document.head.removeChild(element);\n    currentState = null;\n    element = null;\n  }\n}\nfunction setGlobalCursorStyle(state) {\n  if (currentState === state) {\n    return;\n  }\n  currentState = state;\n  const style = getCursorStyle(state);\n  if (element === null) {\n    element = document.createElement(\"style\");\n    document.head.appendChild(element);\n  }\n  element.innerHTML = `*{cursor: ${style}!important;}`;\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\nfunction getPanelElementsForGroup(groupId) {\n  return Array.from(document.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getSerializationKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return `${order}:${JSON.stringify(constraints)}`;\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const serialized = storage.getItem(`PanelGroup:sizes:${autoSaveId}`);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction loadPanelLayout(autoSaveId, panels, storage) {\n  const state = loadSerializedPanelGroupState(autoSaveId, storage);\n  if (state) {\n    var _state$key;\n    const key = getSerializationKey(panels);\n    return (_state$key = state[key]) !== null && _state$key !== void 0 ? _state$key : null;\n  }\n  return null;\n}\nfunction savePanelGroupLayout(autoSaveId, panels, sizes, storage) {\n  const key = getSerializationKey(panels);\n  const state = loadSerializedPanelGroupState(autoSaveId, storage) || {};\n  state[key] = sizes;\n  try {\n    storage.setItem(`PanelGroup:sizes:${autoSaveId}`, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction shouldMonitorPixelBasedConstraints(constraints) {\n  return constraints.some(constraints => {\n    return constraints.collapsedSizePixels !== undefined || constraints.maxSizePixels !== undefined || constraints.minSizePixels !== undefined;\n  });\n}\n\nfunction validatePanelConstraints({\n  groupSizePixels,\n  panelConstraints,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    {\n      const {\n        collapsedSizePercentage,\n        collapsedSizePixels,\n        defaultSizePercentage,\n        defaultSizePixels,\n        maxSizePercentage,\n        maxSizePixels,\n        minSizePercentage,\n        minSizePixels\n      } = panelConstraints[panelIndex];\n      const conflictingUnits = [];\n      if (collapsedSizePercentage != null && collapsedSizePixels != null) {\n        conflictingUnits.push(\"collapsed size\");\n      }\n      if (defaultSizePercentage != null && defaultSizePixels != null) {\n        conflictingUnits.push(\"default size\");\n      }\n      if (maxSizePercentage != null && maxSizePixels != null) {\n        conflictingUnits.push(\"max size\");\n      }\n      if (minSizePercentage != null && minSizePixels != null) {\n        conflictingUnits.push(\"min size\");\n      }\n      if (conflictingUnits.length > 0) {\n        warnings.push(`should not specify both percentage and pixel units for: ${conflictingUnits.join(\", \")}`);\n      }\n    }\n    {\n      const {\n        collapsedSizePercentage,\n        defaultSizePercentage,\n        maxSizePercentage,\n        minSizePercentage\n      } = computePercentagePanelConstraints(panelConstraints, panelIndex, groupSizePixels);\n      if (minSizePercentage > maxSizePercentage) {\n        warnings.push(`min size (${minSizePercentage}%) should not be greater than max size (${maxSizePercentage}%)`);\n      }\n      if (defaultSizePercentage != null) {\n        if (defaultSizePercentage < 0) {\n          warnings.push(\"default size should not be less than 0\");\n        } else if (defaultSizePercentage < minSizePercentage) {\n          warnings.push(\"default size should not be less than min size\");\n        }\n        if (defaultSizePercentage > 100) {\n          warnings.push(\"default size should not be greater than 100\");\n        } else if (defaultSizePercentage > maxSizePercentage) {\n          warnings.push(\"default size should not be greater than max size\");\n        }\n      }\n      if (collapsedSizePercentage > minSizePercentage) {\n        warnings.push(\"collapsed size should not be greater than min size\");\n      }\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  groupSizePixels,\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayout.reduce((accumulated, current) => accumulated + current, 0), 100)) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    const safeSize = resizePanel({\n      groupSizePixels,\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        groupSizePixels,\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  dataAttributes,\n  direction,\n  forwardedRef,\n  id: idFromProps,\n  onLayout = null,\n  keyboardResizeByPercentage = null,\n  keyboardResizeByPixels = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\"\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const [dragState, setDragState] = useState(null);\n  const [layout, setLayout] = useState([]);\n  const panelIdToLastNotifiedMixedSizesMapRef = useRef({});\n  const panelSizeBeforeCollapseRef = useRef(new Map());\n  const prevDeltaRef = useRef(0);\n  const committedValuesRef = useRef({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeByPercentage,\n    keyboardResizeByPixels,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = useRef({\n    layout,\n    panelDataArray: []\n  });\n  const devWarningsRef = useRef({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  useImperativeHandle(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        id: groupId\n      } = committedValuesRef.current;\n      const {\n        layout\n      } = eagerValuesRef.current;\n      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n      return layout.map(sizePercentage => {\n        return {\n          sizePercentage,\n          sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n        };\n      });\n    },\n    setLayout: mixedSizes => {\n      const {\n        id: groupId,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n      const unsafeLayout = mixedSizes.map(mixedSize => getPercentageSizeFromMixedSizes(mixedSize, groupSizePixels));\n      const safeLayout = validatePanelGroupLayout({\n        groupSizePixels,\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout.map(sizePercentage => ({\n            sizePercentage,\n            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n          })));\n        }\n        callPanelCallbacks(groupId, panelDataArray, safeLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n      }\n    }\n  }), []);\n  useIsomorphicLayoutEffect(() => {\n    committedValuesRef.current.autoSaveId = autoSaveId;\n    committedValuesRef.current.direction = direction;\n    committedValuesRef.current.dragState = dragState;\n    committedValuesRef.current.id = groupId;\n    committedValuesRef.current.onLayout = onLayout;\n    committedValuesRef.current.storage = storage;\n\n    // panelDataArray and layout are updated in-sync with scheduled state updates.\n    // TODO [217] Move these values into a separate ref\n  });\n\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout\n  });\n  useEffect(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n\n      // Limit the frequency of localStorage updates.\n      if (!debounceMap[autoSaveId]) {\n        debounceMap[autoSaveId] = debounce(savePanelGroupLayout, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n      }\n      debounceMap[autoSaveId](autoSaveId, panelDataArray, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const constraints = panelDataArray.map(({\n      constraints\n    }) => constraints);\n    if (!shouldMonitorPixelBasedConstraints(constraints)) {\n      // Avoid the overhead of ResizeObserver if no pixel constraints require monitoring\n      return;\n    }\n    if (typeof ResizeObserver === \"undefined\") {\n      console.warn(`WARNING: Pixel based constraints require ResizeObserver but it is not supported by the current browser.`);\n    } else {\n      const resizeObserver = new ResizeObserver(() => {\n        const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n        const {\n          onLayout\n        } = committedValuesRef.current;\n        const nextLayout = validatePanelGroupLayout({\n          groupSizePixels,\n          layout: prevLayout,\n          panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n        });\n        if (!areEqual(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout.map(sizePercentage => ({\n              sizePercentage,\n              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n            })));\n          }\n          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n        }\n      });\n      resizeObserver.observe(getPanelGroupElement(groupId));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }\n  }, [groupId]);\n\n  // DEV warnings\n  useEffect(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const isValid = validatePanelConstraints({\n            groupSizePixels,\n            panelConstraints,\n            panelId: panelDataArray[panelIndex].id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = useCallback(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSizePercentage,\n        panelSizePercentage,\n        pivotIndices,\n        groupSizePixels\n      } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);\n      if (panelSizePercentage !== collapsedSizePercentage) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSizePercentage);\n        const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSizePercentage - collapsedSizePercentage : collapsedSizePercentage - panelSizePercentage;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          groupSizePixels,\n          layout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout.map(sizePercentage => ({\n              sizePercentage,\n              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n            })));\n          }\n          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n        }\n      }\n    }\n  }, [groupId]);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = useCallback(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSizePercentage,\n        panelSizePercentage,\n        minSizePercentage,\n        pivotIndices,\n        groupSizePixels\n      } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);\n      if (panelSizePercentage === collapsedSizePercentage) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSizePercentage = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSizePercentage = prevPanelSizePercentage != null && prevPanelSizePercentage >= minSizePercentage ? prevPanelSizePercentage : minSizePercentage;\n        const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSizePercentage - baseSizePercentage : baseSizePercentage - panelSizePercentage;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          groupSizePixels,\n          layout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout.map(sizePercentage => ({\n              sizePercentage,\n              sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n            })));\n          }\n          callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n        }\n      }\n    }\n  }, [groupId]);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSizePercentage,\n      panelSizePixels\n    } = panelDataHelper(groupId, panelDataArray, panelData, layout);\n    return {\n      sizePercentage: panelSizePercentage,\n      sizePixels: panelSizePixels\n    };\n  }, [groupId]);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = panelDataArray.indexOf(panelData);\n    return computePanelFlexBoxStyle({\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSizePercentage,\n      collapsible,\n      panelSizePercentage\n    } = panelDataHelper(groupId, panelDataArray, panelData, layout);\n    return collapsible === true && panelSizePercentage === collapsedSizePercentage;\n  }, [groupId]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSizePercentage,\n      collapsible,\n      panelSizePercentage\n    } = panelDataHelper(groupId, panelDataArray, panelData, layout);\n    return !collapsible || panelSizePercentage > collapsedSizePercentage;\n  }, [groupId]);\n  const registerPanel = useCallback(panelData => {\n    const {\n      autoSaveId,\n      id: groupId,\n      onLayout,\n      storage\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n\n    // Wait until all panels have registered before we try to compute layout;\n    // doing it earlier is both wasteful and may trigger misleading warnings in development mode.\n    const panelElements = getPanelElementsForGroup(groupId);\n    if (panelElements.length !== panelDataArray.length) {\n      return;\n    }\n\n    // If this panel has been configured to persist sizing information,\n    // default size should be restored from local storage if possible.\n    let unsafeLayout = null;\n    if (autoSaveId) {\n      unsafeLayout = loadPanelLayout(autoSaveId, panelDataArray, storage);\n    }\n    const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n    if (groupSizePixels <= 0) {\n      if (shouldMonitorPixelBasedConstraints(panelDataArray.map(({\n        constraints\n      }) => constraints))) {\n        // Wait until the group has rendered a non-zero size before computing layout.\n        return;\n      }\n    }\n    if (unsafeLayout == null) {\n      unsafeLayout = calculateUnsafeDefaultLayout({\n        groupSizePixels,\n        panelDataArray\n      });\n    }\n\n    // Validate even saved layouts in case something has changed since last render\n    // e.g. for pixel groups, this could be the size of the window\n    const nextLayout = validatePanelGroupLayout({\n      groupSizePixels,\n      layout: unsafeLayout,\n      panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n    });\n\n    // Offscreen mode makes this a bit weird;\n    // Panels unregister when hidden and re-register when shown again,\n    // but the overall layout doesn't change between these two cases.\n    setLayout(nextLayout);\n    eagerValuesRef.current.layout = nextLayout;\n    if (!areEqual(prevLayout, nextLayout)) {\n      if (onLayout) {\n        onLayout(nextLayout.map(sizePercentage => ({\n          sizePercentage,\n          sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n        })));\n      }\n      callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n    }\n  }, []);\n  const registerResizeHandle = useCallback(dragHandleId => {\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeByPercentage,\n        keyboardResizeByPixels,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId);\n      let delta = calculateDeltaPercentage(event, groupId, dragHandleId, direction, dragState, {\n        percentage: keyboardResizeByPercentage,\n        pixels: keyboardResizeByPixels\n      });\n      if (delta === 0) {\n        return;\n      }\n\n      // Support RTL layouts\n      const isHorizontal = direction === \"horizontal\";\n      if (document.dir === \"rtl\" && isHorizontal) {\n        delta = -delta;\n      }\n      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        groupSizePixels,\n        layout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isMouseEvent(event) || isTouchEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged) {\n            // If the pointer has moved too far to resize the panel any further,\n            // update the cursor style for a visual clue.\n            // This mimics VS Code behavior.\n\n            if (isHorizontal) {\n              setGlobalCursorStyle(delta < 0 ? \"horizontal-min\" : \"horizontal-max\");\n            } else {\n              setGlobalCursorStyle(delta < 0 ? \"vertical-min\" : \"vertical-max\");\n            }\n          } else {\n            // Reset the cursor style to the the normal resize cursor.\n            setGlobalCursorStyle(isHorizontal ? \"horizontal\" : \"vertical\");\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout.map(sizePercentage => ({\n            sizePercentage,\n            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n          })));\n        }\n        callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = useCallback((panelData, mixedSizes) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      groupSizePixels,\n      panelSizePercentage,\n      pivotIndices\n    } = panelDataHelper(groupId, panelDataArray, panelData, prevLayout);\n    const sizePercentage = getPercentageSizeFromMixedSizes(mixedSizes, groupSizePixels);\n    const isLastPanel = panelDataArray.indexOf(panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSizePercentage - sizePercentage : sizePercentage - panelSizePercentage;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      groupSizePixels,\n      layout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout.map(sizePercentage => ({\n          sizePercentage,\n          sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n        })));\n      }\n      callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n    }\n  }, [groupId]);\n  const startDragging = useCallback((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    const handleElement = getResizeHandleElement(dragHandleId);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = useCallback(() => {\n    resetGlobalCursorStyle();\n    setDragState(null);\n  }, []);\n  const unregisterPanelRef = useRef({\n    pendingPanelIds: new Set(),\n    timeout: null\n  });\n  const unregisterPanel = useCallback(panelData => {\n    const {\n      id: groupId,\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = panelDataArray.indexOf(panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n      unregisterPanelRef.current.pendingPanelIds.add(panelData.id);\n    }\n    if (unregisterPanelRef.current.timeout != null) {\n      clearTimeout(unregisterPanelRef.current.timeout);\n    }\n\n    // Batch panel unmounts so that we only calculate layout once;\n    // This is more efficient and avoids misleading warnings in development mode.\n    // We can't check the DOM to detect this because Panel elements have not yet been removed.\n    unregisterPanelRef.current.timeout = setTimeout(() => {\n      const {\n        pendingPanelIds\n      } = unregisterPanelRef.current;\n      const map = panelIdToLastNotifiedMixedSizesMapRef.current;\n\n      // TRICKY\n      // Strict effects mode\n      let unmountDueToStrictMode = false;\n      pendingPanelIds.forEach(panelId => {\n        pendingPanelIds.delete(panelId);\n        if (panelDataArray.find(({\n          id\n        }) => id === panelId) == null) {\n          unmountDueToStrictMode = true;\n\n          // TRICKY\n          // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n          // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n          // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n          delete map[panelData.id];\n        }\n      });\n      if (!unmountDueToStrictMode) {\n        return;\n      }\n      if (panelDataArray.length === 0) {\n        // The group is unmounting; skip layout calculation.\n        return;\n      }\n      const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n      let unsafeLayout = calculateUnsafeDefaultLayout({\n        groupSizePixels,\n        panelDataArray\n      });\n\n      // Validate even saved layouts in case something has changed since last render\n      // e.g. for pixel groups, this could be the size of the window\n      const nextLayout = validatePanelGroupLayout({\n        groupSizePixels,\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, nextLayout)) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout.map(sizePercentage => ({\n            sizePercentage,\n            sizePixels: convertPercentageToPixels(sizePercentage, groupSizePixels)\n          })));\n        }\n        callPanelCallbacks(groupId, panelDataArray, nextLayout, panelIdToLastNotifiedMixedSizesMapRef.current);\n      }\n    }, 0);\n  }, []);\n  const context = useMemo(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return createElement(PanelGroupContext.Provider, {\n    value: context\n  }, createElement(Type, {\n    children,\n    className: classNameFromProps,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    ...dataAttributes,\n    // CSS selectors\n    \"data-panel-group\": \"\",\n    \"data-panel-group-direction\": direction,\n    \"data-panel-group-id\": groupId\n  }));\n}\nconst PanelGroup = forwardRef((props, ref) => createElement(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction panelDataHelper(groupId, panelDataArray, panelData, layout) {\n  const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n  const panelIndex = panelDataArray.indexOf(panelData);\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  const groupSizePixels = calculateAvailablePanelSizeInPixels(groupId);\n  const percentagePanelConstraints = computePercentagePanelConstraints(panelConstraintsArray, panelIndex, groupSizePixels);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSizePercentage = layout[panelIndex];\n  const panelSizePixels = convertPercentageToPixels(panelSizePercentage, groupSizePixels);\n  return {\n    ...percentagePanelConstraints,\n    collapsible: panelConstraints.collapsible,\n    panelSizePercentage,\n    panelSizePixels,\n    groupSizePixels,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler\n}) {\n  useEffect(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(\"data-panel-group-id\");\n            const handles = getResizeHandleElementsForGroup(groupId);\n            const index = getResizeHandleElementIndex(groupId, handleId);\n            assert(index !== null);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  dataAttributes,\n  disabled = false,\n  id: idFromProps = null,\n  onDragging,\n  style: styleFromProps = {},\n  tagName: Type = \"div\"\n}) {\n  const divElementRef = useRef(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = useRef({\n    onDragging\n  });\n  useEffect(() => {\n    callbacksRef.current.onDragging = onDragging;\n  });\n  const panelGroupContext = useContext(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    dragState,\n    groupId,\n    registerResizeHandle,\n    startDragging,\n    stopDragging\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const isDragging = (dragState === null || dragState === void 0 ? void 0 : dragState.dragHandleId) === resizeHandleId;\n  const [isFocused, setIsFocused] = useState(false);\n  const [resizeHandler, setResizeHandler] = useState(null);\n  const stopDraggingAndBlur = useCallback(() => {\n    // Clicking on the drag handle shouldn't leave it focused;\n    // That would cause the PanelGroup to think it was still active.\n    const div = divElementRef.current;\n    div.blur();\n    stopDragging();\n    const {\n      onDragging\n    } = callbacksRef.current;\n    if (onDragging) {\n      onDragging(false);\n    }\n  }, [stopDragging]);\n  useEffect(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandle(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandle]);\n  useEffect(() => {\n    if (disabled || resizeHandler == null || !isDragging) {\n      return;\n    }\n    const onMove = event => {\n      resizeHandler(event);\n    };\n    const onMouseLeave = event => {\n      resizeHandler(event);\n    };\n    const divElement = divElementRef.current;\n    const targetDocument = divElement.ownerDocument;\n    targetDocument.body.addEventListener(\"contextmenu\", stopDraggingAndBlur);\n    targetDocument.body.addEventListener(\"mousemove\", onMove);\n    targetDocument.body.addEventListener(\"touchmove\", onMove);\n    targetDocument.body.addEventListener(\"mouseleave\", onMouseLeave);\n    window.addEventListener(\"mouseup\", stopDraggingAndBlur);\n    window.addEventListener(\"touchend\", stopDraggingAndBlur);\n    return () => {\n      targetDocument.body.removeEventListener(\"contextmenu\", stopDraggingAndBlur);\n      targetDocument.body.removeEventListener(\"mousemove\", onMove);\n      targetDocument.body.removeEventListener(\"touchmove\", onMove);\n      targetDocument.body.removeEventListener(\"mouseleave\", onMouseLeave);\n      window.removeEventListener(\"mouseup\", stopDraggingAndBlur);\n      window.removeEventListener(\"touchend\", stopDraggingAndBlur);\n    };\n  }, [direction, disabled, isDragging, resizeHandler, stopDraggingAndBlur]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler\n  });\n  const style = {\n    cursor: getCursorStyle(direction),\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return createElement(Type, {\n    children,\n    className: classNameFromProps,\n    onBlur: () => setIsFocused(false),\n    onFocus: () => setIsFocused(true),\n    onMouseDown: event => {\n      startDragging(resizeHandleId, event.nativeEvent);\n      const {\n        onDragging\n      } = callbacksRef.current;\n      if (onDragging) {\n        onDragging(true);\n      }\n    },\n    onMouseUp: stopDraggingAndBlur,\n    onTouchCancel: stopDraggingAndBlur,\n    onTouchEnd: stopDraggingAndBlur,\n    onTouchStart: event => {\n      startDragging(resizeHandleId, event.nativeEvent);\n      const {\n        onDragging\n      } = callbacksRef.current;\n      if (onDragging) {\n        onDragging(true);\n      }\n    },\n    ref: divElementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex: 0,\n    ...dataAttributes,\n    // CSS selectors\n    \"data-panel-group-direction\": direction,\n    \"data-panel-group-id\": groupId,\n    \"data-resize-handle\": \"\",\n    \"data-resize-handle-active\": isDragging ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    \"data-panel-resize-handle-enabled\": !disabled,\n    \"data-panel-resize-handle-id\": resizeHandleId\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nexport { Panel, PanelGroup, PanelResizeHandle };\n"], "mappings": ";;;;;;;;AAAA,YAAuB;AAMvB,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAGJ,IAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC;AAEtC,IAAM,oBAAoB,cAAc,IAAI;AAC5C,kBAAkB,cAAc;AAEhC,IAAM,4BAA4B;AAElC,IAAM,eAAe,OAAO,UAAU,aAAa,QAAQ,MAAM;AACjE,IAAI,UAAU;AACd,SAAS,YAAY,eAAe,MAAM;AACxC,QAAM,cAAc,aAAa;AACjC,QAAM,QAAQ,OAAO,gBAAgB,eAAe,IAAI;AACxD,MAAI,MAAM,YAAY,MAAM;AAC1B,UAAM,UAAU,KAAK;AAAA,EACvB;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe,MAAM;AACjF;AAEA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,WAAW,qBAAqB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,SAAS,OAAO;AAClB,GAAG;AACD,QAAM,UAAU,WAAW,iBAAiB;AAC5C,MAAI,YAAY,MAAM;AACpB,UAAM,MAAM,iEAAiE;AAAA,EAC/E;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,YAAY,WAAW;AACvC,QAAM,eAAe,OAAO;AAAA,IAC1B,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,IAAI;AAAA,IACJ,eAAe,gBAAgB;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,iBAAiB,OAAO;AAAA,IAC5B,iCAAiC;AAAA,EACnC,CAAC;AAID;AACE,QAAI,CAAC,eAAe,QAAQ,gCAAiC;AAAA,EAC/D;AACA,4BAA0B,MAAM;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AACjB,iBAAa,QAAQ,KAAK;AAC1B,iBAAa,QAAQ,gBAAgB,gBAAgB;AACrD,iBAAa,QAAQ,QAAQ;AAC7B,cAAU,aAAa;AACvB,cAAU,WAAW;AACrB,cAAU,WAAW;AACrB,gBAAY,0BAA0B;AACtC,gBAAY,sBAAsB;AAClC,gBAAY,cAAc;AAC1B,gBAAY,wBAAwB;AACpC,gBAAY,oBAAoB;AAChC,gBAAY,oBAAoB;AAChC,gBAAY,gBAAgB;AAC5B,gBAAY,oBAAoB;AAChC,gBAAY,gBAAgB;AAAA,EAC9B,CAAC;AACD,4BAA0B,MAAM;AAC9B,UAAM,YAAY,aAAa;AAC/B,kBAAc,SAAS;AACvB,WAAO,MAAM;AACX,sBAAgB,SAAS;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,OAAO,SAAS,eAAe,eAAe,CAAC;AACnD,sBAAoB,cAAc,OAAO;AAAA,IACvC,UAAU,MAAM;AACd,oBAAc,aAAa,OAAO;AAAA,IACpC;AAAA,IACA,QAAQ,MAAM;AACZ,kBAAY,aAAa,OAAO;AAAA,IAClC;AAAA,IACA,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,IACA,UAAU;AACR,aAAO,aAAa,aAAa,OAAO;AAAA,IAC1C;AAAA,IACA,cAAc;AACZ,aAAO,iBAAiB,aAAa,OAAO;AAAA,IAC9C;AAAA,IACA,aAAa;AACX,aAAO,CAAC,iBAAiB,aAAa,OAAO;AAAA,IAC/C;AAAA,IACA,QAAQ,gBAAc;AACpB,MAAAA,aAAY,aAAa,SAAS,UAAU;AAAA,IAC9C;AAAA,EACF,IAAI,CAAC,eAAe,aAAa,cAAc,kBAAkB,SAASA,YAAW,CAAC;AACtF,QAAM,QAAQ,cAAc,aAAa,OAAO;AAChD,SAAO,cAAc,MAAM;AAAA,IACzB;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA;AAAA,IAEH,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,uBAAuB;AAAA;AAAA,IAEvB,0BAA0B,eAAe;AAAA,IACzC,mBAAmB,WAAW,KAAK,MAAM,QAAQ,EAAE,QAAQ,CAAC;AAAA,EAC9D,CAAC;AACH;AACA,IAAM,QAAQ,WAAW,CAAC,OAAO,QAAQ,cAAc,uBAAuB;AAAA,EAC5E,GAAG;AAAA,EACH,cAAc;AAChB,CAAC,CAAC;AACF,sBAAsB,cAAc;AACpC,MAAM,cAAc;AAEpB,SAAS,0BAA0B,QAAQ,iBAAiB;AAC1D,SAAO,SAAS,kBAAkB;AACpC;AAEA,SAAS,qCAAqC,kBAAkB,iBAAiB;AAC/E,MAAI;AAAA,IACF,0BAA0B;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,EACF,IAAI;AACJ,QAAM,sBAAsB,uBAAuB,QAAQ,qBAAqB,QAAQ,iBAAiB,QAAQ,iBAAiB;AAClI,MAAI,uBAAuB,mBAAmB,GAAG;AAC/C,YAAQ,KAAK,gCAAgC,eAAe,IAAI;AAChE,WAAO;AAAA,MACL,yBAAyB;AAAA,MACzB;AAAA,MACA,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACrB;AAAA,EACF;AACA,MAAI,uBAAuB,MAAM;AAC/B,8BAA0B,0BAA0B,qBAAqB,eAAe;AAAA,EAC1F;AACA,MAAI,qBAAqB,MAAM;AAC7B,4BAAwB,0BAA0B,mBAAmB,eAAe;AAAA,EACtF;AACA,MAAI,iBAAiB,MAAM;AACzB,wBAAoB,0BAA0B,eAAe,eAAe;AAAA,EAC9E;AACA,MAAI,iBAAiB,MAAM;AACzB,wBAAoB,0BAA0B,eAAe,eAAe;AAAA,EAC9E;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kCAAkC,uBAAuB,YAAY,iBAAiB;AAE7F,MAAI,sBAAsB;AAC1B,MAAI,sBAAsB;AAC1B,WAAS,QAAQ,GAAG,QAAQ,sBAAsB,QAAQ,SAAS;AACjE,QAAI,UAAU,YAAY;AACxB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,sBAAsB,KAAK;AAC/B,YAAM;AAAA,QACJ,yBAAAC;AAAA,QACA,mBAAAC;AAAA,QACA,mBAAAC;AAAA,MACF,IAAI,qCAAqC,sBAAsB,KAAK,GAAG,eAAe;AACtF,6BAAuBD;AACvB,6BAAuB,cAAcD,2BAA0BE;AAAA,IACjE;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,qCAAqC,sBAAsB,UAAU,GAAG,eAAe;AAC3F,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,mBAAmB,sBAAsB,SAAS,IAAI,KAAK,IAAI,mBAAmB,MAAM,mBAAmB,IAAI;AAAA,IAC/G,mBAAmB,sBAAsB,SAAS,IAAI,KAAK,IAAI,mBAAmB,MAAM,mBAAmB,IAAI;AAAA,EACjH;AACF;AAEA,IAAM,YAAY;AAElB,SAAS,oBAAoB,QAAQ,UAAU,iBAAiB,WAAW;AACzE,WAAS,WAAW,OAAO,QAAQ,cAAc,CAAC;AAClD,aAAW,WAAW,SAAS,QAAQ,cAAc,CAAC;AACtD,QAAM,QAAQ,SAAS;AACvB,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT,OAAO;AACL,WAAO,QAAQ,IAAI,IAAI;AAAA,EACzB;AACF;AAEA,SAAS,kBAAkB,QAAQ,UAAU,gBAAgB;AAC3D,SAAO,oBAAoB,QAAQ,UAAU,cAAc,MAAM;AACnE;AAGA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,sBAAsB,iBAAiB,KAAK,CAAC;AAAA,IACjD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM,uBAAuB,QAAQ,qBAAqB,QAAQ,iBAAiB,QAAQ,iBAAiB,IAAI;AAChH,MAAI,uBAAuB,mBAAmB,GAAG;AAC/C,YAAQ,KAAK,gCAAgC,eAAe,IAAI;AAChE,WAAO;AAAA,EACT;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kCAAkC,kBAAkB,YAAY,eAAe;AACnF,MAAI,qBAAqB,MAAM;AAC7B,QAAI,oBAAoB,MAAM,iBAAiB,IAAI,GAAG;AACpD,UAAI,aAAa;AAEf,cAAM,gBAAgB,0BAA0B,qBAAqB;AACrE,YAAI,oBAAoB,MAAM,YAAY,IAAI,GAAG;AAC/C,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,qBAAqB,MAAM;AAC7B,WAAO,KAAK,IAAI,mBAAmB,IAAI;AAAA,EACzC;AACA,SAAO;AACT;AAGA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,kBAAkB,OAAO,CAAC,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,MAAI,eAAe;AAiBnB;AAGE,QAAI,YAAY,YAAY;AAC1B;AAEE,cAAM,QAAQ,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAC1D,cAAM,cAAc,iBAAiB,KAAK;AAG1C,YAAI,YAAY,aAAa;AAC3B,gBAAM,WAAW,WAAW,KAAK;AACjC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,kCAAkC,kBAAkB,OAAO,eAAe;AAC9E,cAAI,kBAAkB,UAAU,uBAAuB,GAAG;AACxD,kBAAM,aAAa,oBAAoB;AAGvC,gBAAI,oBAAoB,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AACxD,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YAEvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA;AAEE,cAAM,QAAQ,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAC1D,cAAM,cAAc,iBAAiB,KAAK;AAG1C,YAAI,YAAY,aAAa;AAC3B,gBAAM,WAAW,WAAW,KAAK;AACjC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,kCAAkC,kBAAkB,OAAO,eAAe;AAC9E,cAAI,kBAAkB,UAAU,iBAAiB,GAAG;AAClD,kBAAM,aAAa,WAAW;AAG9B,gBAAI,oBAAoB,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AACxD,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YAEvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EAEF;AAEA;AAOE,UAAM,YAAY,QAAQ,IAAI,IAAI;AAClC,QAAI,QAAQ,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AACxD,QAAI,oBAAoB;AAGxB,WAAO,MAAM;AACX,YAAM,WAAW,WAAW,KAAK;AACjC,YAAM,cAAc,YAAY;AAAA,QAC9B;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,YAAMC,SAAQ,cAAc;AAG5B,2BAAqBA;AACrB,eAAS;AACT,UAAI,QAAQ,KAAK,SAAS,iBAAiB,QAAQ;AACjD;AAAA,MACF;AAAA,IACF;AAGA,UAAM,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,iBAAiB,CAAC;AACzE,YAAQ,QAAQ,IAAI,IAAI,cAAc;AAAA,EAGxC;AAEA;AAGE,UAAM,aAAa,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAC/D,QAAI,QAAQ;AACZ,WAAO,SAAS,KAAK,QAAQ,iBAAiB,QAAQ;AACpD,YAAM,iBAAiB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,YAAY;AAC9D,YAAM,WAAW,WAAW,KAAK;AACjC,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,YAAY;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,UAAI,CAAC,kBAAkB,UAAU,QAAQ,GAAG;AAC1C,wBAAgB,WAAW;AAC3B,mBAAW,KAAK,IAAI;AACpB,YAAI,aAAa,YAAY,CAAC,EAAE,cAAc,KAAK,IAAI,KAAK,EAAE,YAAY,CAAC,GAAG,QAAW;AAAA,UACvF,SAAS;AAAA,QACX,CAAC,KAAK,GAAG;AACP;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,GAAG;AACb;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAOA,MAAI,kBAAkB,cAAc,CAAC,GAAG;AAEtC,WAAO;AAAA,EACT;AACA;AAEE,UAAM,aAAa,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAC/D,UAAM,aAAa,WAAW,UAAU,IAAI;AAC5C,UAAM,WAAW,YAAY;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAGD,eAAW,UAAU,IAAI;AAGzB,QAAI,CAAC,kBAAkB,UAAU,UAAU,GAAG;AAC5C,UAAI,iBAAiB,aAAa;AAClC,YAAMC,cAAa,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAC/D,UAAI,QAAQA;AACZ,aAAO,SAAS,KAAK,QAAQ,iBAAiB,QAAQ;AACpD,cAAM,WAAW,WAAW,KAAK;AACjC,cAAMC,cAAa,WAAW;AAC9B,cAAMC,YAAW,YAAY;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,YAAY;AAAA,UACZ,MAAMD;AAAA,QACR,CAAC;AACD,YAAI,CAAC,kBAAkB,UAAUC,SAAQ,GAAG;AAC1C,4BAAkBA,YAAW;AAC7B,qBAAW,KAAK,IAAIA;AAAA,QACtB;AACA,YAAI,kBAAkB,gBAAgB,CAAC,GAAG;AACxC;AAAA,QACF;AACA,YAAI,QAAQ,GAAG;AACb;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAKA,QAAM,YAAY,WAAW,OAAO,CAAC,OAAO,SAAS,OAAO,OAAO,CAAC;AACpE,iBAAe,MAAM;AAKrB,MAAI,CAAC,kBAAkB,WAAW,GAAG,GAAG;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,OAAO,mBAAmB,UAAU,qBAAqB;AAChE,MAAI,CAAC,mBAAmB;AACtB,YAAQ,MAAM,OAAO;AACrB,UAAM,MAAM,OAAO;AAAA,EACrB;AACF;AAEA,SAAS,gCAAgC;AAAA,EACvC;AAAA,EACA;AACF,GAAG,iBAAiB;AAClB,MAAI,kBAAkB,MAAM;AAC1B,WAAO;AAAA,EACT,WAAW,cAAc,MAAM;AAC7B,WAAO,0BAA0B,YAAY,eAAe;AAAA,EAC9D;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,eAAe;AAGnB,cAAY,QAAQ,CAAC,WAAW,UAAU;AACxC,QAAI,uBAAuB;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,wBAAwB,gCAAgC;AAAA,MACvE,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd,GAAG,eAAe,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AAC5F,UAAM,WAAW,yBAAyB,gCAAgC;AAAA,MACxE,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd,GAAG,eAAe,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB;AAC9F,QAAI,UAAU,aAAa,CAAC,GAAG;AAC7B,uBAAiB;AACjB,uBAAiB;AAAA,IACnB,OAAO;AACL,sBAAgB;AAChB,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,OAAO,aAAa,CAAC,CAAC;AACvC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gCAAgC,SAAS;AAChD,SAAO,MAAM,KAAK,SAAS,iBAAiB,sDAAsD,OAAO,IAAI,CAAC;AAChH;AAEA,SAAS,4BAA4B,SAAS,IAAI;AAChD,QAAM,UAAU,gCAAgC,OAAO;AACvD,QAAM,QAAQ,QAAQ,UAAU,YAAU,OAAO,aAAa,6BAA6B,MAAM,EAAE;AACnG,SAAO,UAAU,QAAQ,UAAU,SAAS,QAAQ;AACtD;AAEA,SAAS,sBAAsB,SAAS,cAAc;AACpD,QAAM,QAAQ,4BAA4B,SAAS,YAAY;AAC/D,SAAO,SAAS,OAAO,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;AACrD;AAEA,SAAS,qBAAqB,IAAI;AAChC,QAAMC,WAAU,SAAS,cAAc,2CAA2C,EAAE,IAAI;AACxF,MAAIA,UAAS;AACX,WAAOA;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,oCAAoC,SAAS;AACpD,QAAM,oBAAoB,qBAAqB,OAAO;AACtD,MAAI,qBAAqB,MAAM;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,YAAY,kBAAkB,aAAa,4BAA4B;AAC7E,QAAM,gBAAgB,gCAAgC,OAAO;AAC7D,MAAI,cAAc,cAAc;AAC9B,WAAO,kBAAkB,cAAc,cAAc,OAAO,CAAC,aAAa,WAAW;AACnF,aAAO,cAAc,OAAO;AAAA,IAC9B,GAAG,CAAC;AAAA,EACN,OAAO;AACL,WAAO,kBAAkB,eAAe,cAAc,OAAO,CAAC,aAAa,WAAW;AACpF,aAAO,cAAc,OAAO;AAAA,IAC9B,GAAG,CAAC;AAAA,EACN;AACF;AAEA,SAAS,4BAA4B,SAAS;AAC5C,QAAM,oBAAoB,qBAAqB,OAAO;AACtD,MAAI,qBAAqB,MAAM;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,YAAY,kBAAkB,aAAa,4BAA4B;AAC7E,QAAM,gBAAgB,gCAAgC,OAAO;AAC7D,MAAI,cAAc,cAAc;AAC9B,WAAO,kBAAkB,cAAc,cAAc,OAAO,CAAC,aAAa,WAAW;AACnF,aAAO,cAAc,OAAO;AAAA,IAC9B,GAAG,CAAC;AAAA,EACN,OAAO;AACL,WAAO,kBAAkB,eAAe,cAAc,OAAO,CAAC,aAAa,WAAW;AACpF,aAAO,cAAc,OAAO;AAAA,IAC9B,GAAG,CAAC;AAAA,EACN;AACF;AAEA,SAAS,uBAAuB,IAAI;AAClC,QAAMA,WAAU,SAAS,cAAc,iCAAiC,EAAE,IAAI;AAC9E,MAAIA,UAAS;AACX,WAAOA;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,wBAAwB,SAAS,UAAU,aAAa;AAC/D,MAAI,uBAAuB,oBAAoB,iBAAiB;AAChE,QAAM,SAAS,uBAAuB,QAAQ;AAC9C,QAAM,UAAU,gCAAgC,OAAO;AACvD,QAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM,IAAI;AACjD,QAAM,YAAY,yBAAyB,qBAAqB,YAAY,KAAK,OAAO,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,QAAQ,QAAQ,0BAA0B,SAAS,wBAAwB;AACvO,QAAM,WAAW,mBAAmB,eAAe,YAAY,QAAQ,CAAC,OAAO,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,QAAQ,oBAAoB,SAAS,kBAAkB;AACtM,SAAO,CAAC,UAAU,OAAO;AAC3B;AAIA,SAAS,oCAAoC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,iBAAiB,OAAO;AAAA,IAC5B,iCAAiC;AAAA,EACnC,CAAC;AACD,4BAA0B,MAAM;AAC9B,UAAM,kBAAkB,oCAAoC,OAAO;AACnE,UAAM,uBAAuB,gCAAgC,OAAO;AACpE,aAAS,QAAQ,GAAG,QAAQ,eAAe,SAAS,GAAG,SAAS;AAC9D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,oBAAoB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,cAAc,CAAC,OAAO,QAAQ,CAAC;AAAA,MACjC,CAAC;AACD,YAAM,sBAAsB,qBAAqB,KAAK;AACtD,UAAI,uBAAuB,MAAM;AAC/B;AACE,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,eAAe;AACnB,cAAI,CAAC,iCAAiC;AACpC,2BAAe,QAAQ,kCAAkC;AACzD,oBAAQ,KAAK,kDAAkD,OAAO,GAAG;AAAA,UAC3E;AAAA,QACF;AAAA,MACF,OAAO;AACL,4BAAoB,aAAa,iBAAiB,eAAe,KAAK,EAAE,EAAE;AAC1E,4BAAoB,aAAa,iBAAiB,KAAK,KAAK,MAAM,QAAQ,CAAC;AAC3E,4BAAoB,aAAa,iBAAiB,KAAK,KAAK,MAAM,QAAQ,CAAC;AAC3E,4BAAoB,aAAa,iBAAiB,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,MAC7E;AAAA,IACF;AACA,WAAO,MAAM;AACX,2BAAqB,QAAQ,CAAC,qBAAqB,UAAU;AAC3D,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AAAA,MACrD,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,SAAS,QAAQ,cAAc,CAAC;AACpC,YAAU,MAAM;AACd,UAAM;AAAA,MACJ,gBAAAC;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,eAAe,qBAAqB,OAAO;AACjD,WAAO,gBAAgB,MAAM,0BAA0B,OAAO,GAAG;AACjE,UAAM,UAAU,gCAAgC,OAAO;AACvD,UAAM,mBAAmB,QAAQ,IAAI,YAAU;AAC7C,YAAM,WAAW,OAAO,aAAa,6BAA6B;AAClE,YAAM,CAAC,UAAU,OAAO,IAAI,wBAAwB,SAAS,UAAUA,eAAc;AACrF,UAAI,YAAY,QAAQ,WAAW,MAAM;AACvC,eAAO,MAAM;AAAA,QAAC;AAAA,MAChB;AACA,YAAM,YAAY,WAAS;AACzB,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AACA,gBAAQ,MAAM,KAAK;AAAA,UACjB,KAAK,SACH;AACE,kBAAM,eAAe;AACrB,kBAAM,QAAQA,gBAAe,UAAU,eAAa,UAAU,OAAO,QAAQ;AAC7E,gBAAI,SAAS,GAAG;AACd,oBAAM,YAAYA,gBAAe,KAAK;AACtC,oBAAM,OAAO,OAAO,KAAK;AACzB,kBAAI,QAAQ,QAAQ,UAAU,YAAY,aAAa;AACrD,oBAAI,uBAAuB;AAC3B,sBAAM,kBAAkB,4BAA4B,OAAO;AAC3D,sBAAM,iBAAiB,wBAAwB,gCAAgC;AAAA,kBAC7E,gBAAgB,UAAU,YAAY;AAAA,kBACtC,YAAY,UAAU,YAAY;AAAA,gBACpC,GAAG,eAAe,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AAC5F,sBAAM,WAAW,yBAAyB,gCAAgC;AAAA,kBACxE,gBAAgB,UAAU,YAAY;AAAA,kBACtC,YAAY,UAAU,YAAY;AAAA,gBACpC,GAAG,eAAe,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB;AAC9F,sBAAM,aAAa,oBAAoB;AAAA,kBACrC,OAAO,kBAAkB,MAAM,aAAa,IAAI,UAAU,gBAAgB,gBAAgB;AAAA,kBAC1F;AAAA,kBACA;AAAA,kBACA,kBAAkBA,gBAAe,IAAI,CAAAC,eAAaA,WAAU,WAAW;AAAA,kBACvE,cAAc,sBAAsB,SAAS,QAAQ;AAAA,kBACrD,SAAS;AAAA,gBACX,CAAC;AACD,oBAAI,WAAW,YAAY;AACzB,4BAAU,UAAU;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF;AAAA,QACJ;AAAA,MACF;AACA,aAAO,iBAAiB,WAAW,SAAS;AAC5C,aAAO,MAAM;AACX,eAAO,oBAAoB,WAAW,SAAS;AAAA,MACjD;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,uBAAiB,QAAQ,qBAAmB,gBAAgB,CAAC;AAAA,IAC/D;AAAA,EACF,GAAG,CAAC,oBAAoB,gBAAgB,SAAS,QAAQ,gBAAgB,SAAS,CAAC;AACrF;AAEA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,OAAO,WAAW,OAAO,QAAQ;AACnC,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,QAAI,OAAO,KAAK,MAAM,OAAO,KAAK,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,SAAS;AACxB;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,KAAK,WAAW,OAAO;AACtC;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,KAAK,WAAW,OAAO;AACtC;AAEA,SAAS,6BAA6B,WAAW,OAAO;AACtD,QAAM,eAAe,cAAc;AACnC,MAAI,aAAa,KAAK,GAAG;AACvB,WAAO,eAAe,MAAM,UAAU,MAAM;AAAA,EAC9C,WAAW,aAAa,KAAK,GAAG;AAC9B,UAAM,aAAa,MAAM,QAAQ,CAAC;AAClC,WAAO,eAAe,WAAW,UAAU,WAAW;AAAA,EACxD,OAAO;AACL,UAAM,MAAM,2BAA2B,MAAM,IAAI,GAAG;AAAA,EACtD;AACF;AAEA,SAAS,8BAA8B,OAAO,cAAc,WAAW,kBAAkB;AACvF,QAAM,eAAe,cAAc;AACnC,QAAM,gBAAgB,uBAAuB,YAAY;AACzD,QAAM,UAAU,cAAc,aAAa,qBAAqB;AAChE,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,6BAA6B,WAAW,KAAK;AACpE,QAAM,eAAe,qBAAqB,OAAO;AACjD,QAAM,YAAY,aAAa,sBAAsB;AACrD,QAAM,oBAAoB,eAAe,UAAU,QAAQ,UAAU;AACrE,QAAM,eAAe,iBAAiB;AACtC,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,SAAO;AACT;AAGA,SAAS,yBAAyB,OAAO,SAAS,cAAc,WAAW,kBAAkB,yBAAyB;AACpH,MAAI,UAAU,KAAK,GAAG;AACpB,UAAM,eAAe,cAAc;AACnC,UAAM,eAAe,qBAAqB,OAAO;AACjD,UAAM,OAAO,aAAa,sBAAsB;AAChD,UAAM,oBAAoB,eAAe,KAAK,QAAQ,KAAK;AAC3D,QAAI,QAAQ;AACZ,QAAI,MAAM,UAAU;AAClB,cAAQ;AAAA,IACV,WAAW,wBAAwB,cAAc,MAAM;AACrD,cAAQ,wBAAwB;AAAA,IAClC,WAAW,wBAAwB,UAAU,MAAM;AACjD,cAAQ,wBAAwB,SAAS;AAAA,IAC3C,OAAO;AACL,cAAQ;AAAA,IACV;AACA,QAAI,WAAW;AACf,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AACH,mBAAW,eAAe,IAAI;AAC9B;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,CAAC,QAAQ;AACnC;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,QAAQ;AAClC;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,IAAI,CAAC;AAC/B;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,IACJ;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO,8BAA8B,OAAO,cAAc,WAAW,gBAAgB;AAAA,EACvF;AACF;AAEA,SAAS,6BAA6B;AAAA,EACpC;AAAA,EACA;AACF,GAAG;AACD,QAAM,SAAS,MAAM,eAAe,MAAM;AAC1C,QAAM,uBAAuB,eAAe,IAAI,eAAa,UAAU,WAAW;AAClF,MAAI,qBAAqB;AACzB,MAAI,gBAAgB;AAGpB,WAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,kCAAkC,sBAAsB,OAAO,eAAe;AAClF,QAAI,yBAAyB,MAAM;AACjC;AACA,aAAO,KAAK,IAAI;AAChB,uBAAiB;AAAA,IACnB;AAAA,EACF;AAGA,WAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,kCAAkC,sBAAsB,OAAO,eAAe;AAClF,QAAI,yBAAyB,MAAM;AACjC;AAAA,IACF;AACA,UAAM,qBAAqB,eAAe,SAAS;AACnD,UAAM,OAAO,gBAAgB;AAC7B;AACA,WAAO,KAAK,IAAI;AAChB,qBAAiB;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,0BAA0B,YAAY,iBAAiB;AAC9D,SAAO,aAAa,MAAM;AAC5B;AAGA,SAAS,mBAAmB,SAAS,aAAa,QAAQ,oCAAoC;AAC5F,QAAM,kBAAkB,oCAAoC,OAAO;AACnE,SAAO,QAAQ,CAAC,gBAAgB,UAAU;AACxC,UAAM,YAAY,YAAY,KAAK;AACnC,QAAI,CAAC,WAAW;AAGd;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,aAAa;AAAA,MACjB;AAAA,MACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,IACvE;AACA,UAAM,yBAAyB,mCAAmC,OAAO;AACzE,QAAI,0BAA0B,QAAQ,WAAW,mBAAmB,uBAAuB,kBAAkB,WAAW,eAAe,uBAAuB,YAAY;AACxK,yCAAmC,OAAO,IAAI;AAC9C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,UAAU;AACZ,iBAAS,YAAY,sBAAsB;AAAA,MAC7C;AACA,UAAI,gBAAgB,cAAc,WAAW;AAC3C,YAAI;AACJ,cAAM,iBAAiB,wBAAwB,gCAAgC;AAAA,UAC7E,gBAAgB,YAAY;AAAA,UAC5B,YAAY,YAAY;AAAA,QAC1B,GAAG,eAAe,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AAC5F,cAAM,OAAO,gCAAgC,YAAY,eAAe;AACxE,YAAI,aAAa,0BAA0B,QAAQ,uBAAuB,mBAAmB,kBAAkB,SAAS,eAAe;AACrI,mBAAS;AAAA,QACX;AACA,YAAI,eAAe,0BAA0B,QAAQ,uBAAuB,mBAAmB,kBAAkB,SAAS,eAAe;AACvI,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,GAAG;AAC5B,MAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,WAAO;AAAA,EACT,OAAO;AACL,aAAS,QAAQ,GAAG,QAAQ,EAAE,QAAQ,SAAS;AAC7C,UAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AACd,GAAG;AACD,QAAM,OAAO,OAAO,UAAU;AAC9B,MAAI;AACJ,MAAI,UAAU,WAAW,GAAG;AAC1B,eAAW;AAAA,EACb,WAAW,QAAQ,MAAM;AAEvB,eAAW;AAAA,EACb,OAAO;AACL,eAAW,KAAK,YAAY,SAAS;AAAA,EACvC;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA,YAAY;AAAA;AAAA,IAEZ,UAAU;AAAA;AAAA;AAAA,IAGV,eAAe,cAAc,OAAO,SAAS;AAAA,EAC/C;AACF;AAEA,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,SAAS,eAAe,OAAO;AAC7B,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF;AACA,SAAS,yBAAyB;AAChC,MAAI,YAAY,MAAM;AACpB,aAAS,KAAK,YAAY,OAAO;AACjC,mBAAe;AACf,cAAU;AAAA,EACZ;AACF;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,iBAAiB,OAAO;AAC1B;AAAA,EACF;AACA,iBAAe;AACf,QAAM,QAAQ,eAAe,KAAK;AAClC,MAAI,YAAY,MAAM;AACpB,cAAU,SAAS,cAAc,OAAO;AACxC,aAAS,KAAK,YAAY,OAAO;AAAA,EACnC;AACA,UAAQ,YAAY,aAAa,KAAK;AACxC;AAEA,SAAS,SAAS,UAAU,aAAa,IAAI;AAC3C,MAAI,YAAY;AAChB,MAAI,WAAW,IAAI,SAAS;AAC1B,QAAI,cAAc,MAAM;AACtB,mBAAa,SAAS;AAAA,IACxB;AACA,gBAAY,WAAW,MAAM;AAC3B,eAAS,GAAG,IAAI;AAAA,IAClB,GAAG,UAAU;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,SAAS;AACzC,SAAO,MAAM,KAAK,SAAS,iBAAiB,qCAAqC,OAAO,IAAI,CAAC;AAC/F;AAMA,SAAS,yBAAyB,eAAe;AAC/C,MAAI;AACF,QAAI,OAAO,iBAAiB,aAAa;AAEvC,oBAAc,UAAU,UAAQ;AAC9B,eAAO,aAAa,QAAQ,IAAI;AAAA,MAClC;AACA,oBAAc,UAAU,CAAC,MAAM,UAAU;AACvC,qBAAa,QAAQ,MAAM,KAAK;AAAA,MAClC;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AACnB,kBAAc,UAAU,MAAM;AAC9B,kBAAc,UAAU,MAAM;AAAA,IAAC;AAAA,EACjC;AACF;AAMA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,OAAO,IAAI,WAAS;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe;AACjB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,GAAG,KAAK,IAAI,KAAK,UAAU,WAAW,CAAC;AAAA,IAChD;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,KAAK,GAAG;AAChD;AACA,SAAS,8BAA8B,YAAY,SAAS;AAC1D,MAAI;AACF,UAAM,aAAa,QAAQ,QAAQ,oBAAoB,UAAU,EAAE;AACnE,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,MAAM,UAAU;AACpC,UAAI,OAAO,WAAW,YAAY,UAAU,MAAM;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AAAA,EAAC;AACjB,SAAO;AACT;AACA,SAAS,gBAAgB,YAAY,QAAQ,SAAS;AACpD,QAAM,QAAQ,8BAA8B,YAAY,OAAO;AAC/D,MAAI,OAAO;AACT,QAAI;AACJ,UAAM,MAAM,oBAAoB,MAAM;AACtC,YAAQ,aAAa,MAAM,GAAG,OAAO,QAAQ,eAAe,SAAS,aAAa;AAAA,EACpF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,YAAY,QAAQ,OAAO,SAAS;AAChE,QAAM,MAAM,oBAAoB,MAAM;AACtC,QAAM,QAAQ,8BAA8B,YAAY,OAAO,KAAK,CAAC;AACrE,QAAM,GAAG,IAAI;AACb,MAAI;AACF,YAAQ,QAAQ,oBAAoB,UAAU,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA,EACzE,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AAAA,EACrB;AACF;AAEA,SAAS,mCAAmC,aAAa;AACvD,SAAO,YAAY,KAAK,CAAAC,iBAAe;AACrC,WAAOA,aAAY,wBAAwB,UAAaA,aAAY,kBAAkB,UAAaA,aAAY,kBAAkB;AAAA,EACnI,CAAC;AACH;AAEA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD;AACE,UAAM,WAAW,CAAC;AAClB;AACE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,iBAAiB,UAAU;AAC/B,YAAM,mBAAmB,CAAC;AAC1B,UAAI,2BAA2B,QAAQ,uBAAuB,MAAM;AAClE,yBAAiB,KAAK,gBAAgB;AAAA,MACxC;AACA,UAAI,yBAAyB,QAAQ,qBAAqB,MAAM;AAC9D,yBAAiB,KAAK,cAAc;AAAA,MACtC;AACA,UAAI,qBAAqB,QAAQ,iBAAiB,MAAM;AACtD,yBAAiB,KAAK,UAAU;AAAA,MAClC;AACA,UAAI,qBAAqB,QAAQ,iBAAiB,MAAM;AACtD,yBAAiB,KAAK,UAAU;AAAA,MAClC;AACA,UAAI,iBAAiB,SAAS,GAAG;AAC/B,iBAAS,KAAK,2DAA2D,iBAAiB,KAAK,IAAI,CAAC,EAAE;AAAA,MACxG;AAAA,IACF;AACA;AACE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,kCAAkC,kBAAkB,YAAY,eAAe;AACnF,UAAI,oBAAoB,mBAAmB;AACzC,iBAAS,KAAK,aAAa,iBAAiB,2CAA2C,iBAAiB,IAAI;AAAA,MAC9G;AACA,UAAI,yBAAyB,MAAM;AACjC,YAAI,wBAAwB,GAAG;AAC7B,mBAAS,KAAK,wCAAwC;AAAA,QACxD,WAAW,wBAAwB,mBAAmB;AACpD,mBAAS,KAAK,+CAA+C;AAAA,QAC/D;AACA,YAAI,wBAAwB,KAAK;AAC/B,mBAAS,KAAK,6CAA6C;AAAA,QAC7D,WAAW,wBAAwB,mBAAmB;AACpD,mBAAS,KAAK,kDAAkD;AAAA,QAClE;AAAA,MACF;AACA,UAAI,0BAA0B,mBAAmB;AAC/C,iBAAS,KAAK,oDAAoD;AAAA,MACpE;AAAA,IACF;AACA,QAAI,SAAS,SAAS,GAAG;AACvB,YAAM,OAAO,WAAW,OAAO,UAAU,OAAO,MAAM;AACtD,cAAQ,KAAK,GAAG,IAAI;AAAA;AAAA,EAAqC,SAAS,KAAK,IAAI,CAAC,EAAE;AAC9E,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA,QAAQ;AAAA,EACR;AACF,GAAG;AACD,QAAM,aAAa,CAAC,GAAG,UAAU;AAGjC,MAAI,WAAW,WAAW,iBAAiB,QAAQ;AACjD,UAAM,MAAM,WAAW,iBAAiB,MAAM,kBAAkB,WAAW,IAAI,UAAQ,GAAG,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,EACjH,WAAW,CAAC,kBAAkB,WAAW,OAAO,CAAC,aAAa,YAAY,cAAc,SAAS,CAAC,GAAG,GAAG,GAAG;AAGzG;AACE,cAAQ,KAAK,uCAAuC,WAAW,IAAI,UAAQ,GAAG,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,IACrG;AAAA,EACF;AACA,MAAI,gBAAgB;AAGpB,WAAS,QAAQ,GAAG,QAAQ,iBAAiB,QAAQ,SAAS;AAC5D,UAAM,aAAa,WAAW,KAAK;AACnC,UAAM,WAAW,YAAY;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,QAAI,cAAc,UAAU;AAC1B,uBAAiB,aAAa;AAC9B,iBAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EACF;AAIA,MAAI,CAAC,kBAAkB,eAAe,CAAC,GAAG;AACxC,aAAS,QAAQ,GAAG,QAAQ,iBAAiB,QAAQ,SAAS;AAC5D,YAAM,WAAW,WAAW,KAAK;AACjC,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,YAAY;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,UAAI,aAAa,UAAU;AACzB,yBAAiB,WAAW;AAC5B,mBAAW,KAAK,IAAI;AAGpB,YAAI,kBAAkB,eAAe,CAAC,GAAG;AACvC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,kCAAkC;AACxC,IAAM,iBAAiB;AAAA,EACrB,SAAS,UAAQ;AACf,6BAAyB,cAAc;AACvC,WAAO,eAAe,QAAQ,IAAI;AAAA,EACpC;AAAA,EACA,SAAS,CAAC,MAAM,UAAU;AACxB,6BAAyB,cAAc;AACvC,mBAAe,QAAQ,MAAM,KAAK;AAAA,EACpC;AACF;AACA,IAAM,cAAc,CAAC;AACrB,SAAS,2BAA2B;AAAA,EAClC,aAAa;AAAA,EACb;AAAA,EACA,WAAW,qBAAqB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS,OAAO;AAClB,GAAG;AACD,QAAM,UAAU,YAAY,WAAW;AACvC,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,IAAI;AAC/C,QAAM,CAAC,QAAQ,SAAS,IAAI,SAAS,CAAC,CAAC;AACvC,QAAM,wCAAwC,OAAO,CAAC,CAAC;AACvD,QAAM,6BAA6B,OAAO,oBAAI,IAAI,CAAC;AACnD,QAAM,eAAe,OAAO,CAAC;AAC7B,QAAM,qBAAqB,OAAO;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,iBAAiB,OAAO;AAAA,IAC5B;AAAA,IACA,gBAAgB,CAAC;AAAA,EACnB,CAAC;AACD,QAAM,iBAAiB,OAAO;AAAA,IAC5B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,cAAc,CAAC;AAAA,EACjB,CAAC;AACD,sBAAoB,cAAc,OAAO;AAAA,IACvC,OAAO,MAAM,mBAAmB,QAAQ;AAAA,IACxC,WAAW,MAAM;AACf,YAAM;AAAA,QACJ,IAAIC;AAAA,MACN,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAAC;AAAA,MACF,IAAI,eAAe;AACnB,YAAM,kBAAkB,oCAAoCD,QAAO;AACnE,aAAOC,QAAO,IAAI,oBAAkB;AAClC,eAAO;AAAA,UACL;AAAA,UACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,QACvE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,WAAW,gBAAc;AACvB,YAAM;AAAA,QACJ,IAAID;AAAA,QACJ,UAAAE;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI,eAAe;AACnB,YAAM,kBAAkB,oCAAoCF,QAAO;AACnE,YAAM,eAAe,WAAW,IAAI,eAAa,gCAAgC,WAAW,eAAe,CAAC;AAC5G,YAAM,aAAa,yBAAyB;AAAA,QAC1C;AAAA,QACA,QAAQ;AAAA,QACR,kBAAkB,eAAe,IAAI,eAAa,UAAU,WAAW;AAAA,MACzE,CAAC;AACD,UAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAIE,WAAU;AACZ,UAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,YACzC;AAAA,YACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,UACvE,EAAE,CAAC;AAAA,QACL;AACA,2BAAmBF,UAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,MACvG;AAAA,IACF;AAAA,EACF,IAAI,CAAC,CAAC;AACN,4BAA0B,MAAM;AAC9B,uBAAmB,QAAQ,aAAa;AACxC,uBAAmB,QAAQ,YAAY;AACvC,uBAAmB,QAAQ,YAAY;AACvC,uBAAmB,QAAQ,KAAK;AAChC,uBAAmB,QAAQ,WAAW;AACtC,uBAAmB,QAAQ,UAAU;AAAA,EAIvC,CAAC;AAED,sCAAoC;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,eAAe,QAAQ;AAAA,IACvC;AAAA,EACF,CAAC;AACD,YAAU,MAAM;AACd,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AAGnB,QAAI,YAAY;AACd,UAAI,OAAO,WAAW,KAAK,OAAO,WAAW,eAAe,QAAQ;AAClE;AAAA,MACF;AAGA,UAAI,CAAC,YAAY,UAAU,GAAG;AAC5B,oBAAY,UAAU,IAAI,SAAS,sBAAsB,+BAA+B;AAAA,MAC1F;AACA,kBAAY,UAAU,EAAE,YAAY,gBAAgB,QAAQ,OAAO;AAAA,IACrE;AAAA,EACF,GAAG,CAAC,YAAY,QAAQ,OAAO,CAAC;AAChC,4BAA0B,MAAM;AAC9B,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,cAAc,eAAe,IAAI,CAAC;AAAA,MACtC,aAAAD;AAAA,IACF,MAAMA,YAAW;AACjB,QAAI,CAAC,mCAAmC,WAAW,GAAG;AAEpD;AAAA,IACF;AACA,QAAI,OAAO,mBAAmB,aAAa;AACzC,cAAQ,KAAK,yGAAyG;AAAA,IACxH,OAAO;AACL,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,cAAM,kBAAkB,oCAAoC,OAAO;AACnE,cAAM;AAAA,UACJ,UAAAG;AAAA,QACF,IAAI,mBAAmB;AACvB,cAAM,aAAa,yBAAyB;AAAA,UAC1C;AAAA,UACA,QAAQ;AAAA,UACR,kBAAkB,eAAe,IAAI,eAAa,UAAU,WAAW;AAAA,QACzE,CAAC;AACD,YAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,oBAAU,UAAU;AACpB,yBAAe,QAAQ,SAAS;AAChC,cAAIA,WAAU;AACZ,YAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,cACzC;AAAA,cACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,YACvE,EAAE,CAAC;AAAA,UACL;AACA,6BAAmB,SAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,QACvG;AAAA,MACF,CAAC;AACD,qBAAe,QAAQ,qBAAqB,OAAO,CAAC;AACpD,aAAO,MAAM;AACX,uBAAe,WAAW;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAGZ,YAAU,MAAM;AACd;AACE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,eAAe;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,eAAe;AACnB,UAAI,CAAC,yBAAyB;AAC5B,cAAM,WAAW,eAAe,IAAI,CAAC;AAAA,UACnC;AAAA,QACF,MAAM,EAAE;AACR,uBAAe,QAAQ,eAAe;AACtC,cAAM,oBAAoB,aAAa,SAAS,KAAK,CAAC,SAAS,cAAc,QAAQ;AACrF,YAAI,mBAAmB;AACrB,cAAI,eAAe,KAAK,CAAC;AAAA,YACvB;AAAA,YACA;AAAA,UACF,MAAM,CAAC,iBAAiB,SAAS,IAAI,GAAG;AACtC,2BAAe,QAAQ,0BAA0B;AACjD,oBAAQ,KAAK,oFAAoF;AAAA,UACnG;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,+BAA+B;AAClC,cAAM,mBAAmB,eAAe,IAAI,eAAa,UAAU,WAAW;AAC9E,cAAM,kBAAkB,oCAAoC,OAAO;AACnE,iBAAS,aAAa,GAAG,aAAa,iBAAiB,QAAQ,cAAc;AAC3E,gBAAM,UAAU,yBAAyB;AAAA,YACvC;AAAA,YACA;AAAA,YACA,SAAS,eAAe,UAAU,EAAE;AAAA,YACpC;AAAA,UACF,CAAC;AACD,cAAI,CAAC,SAAS;AACZ,2BAAe,QAAQ,gCAAgC;AACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,QAAM,gBAAgB,YAAY,eAAa;AAC7C,UAAM;AAAA,MACJ,UAAAA;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,UAAU,YAAY,aAAa;AACrC,YAAM,wBAAwB,eAAe,IAAI,CAAAJ,eAAaA,WAAU,WAAW;AACnF,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAW,UAAU;AAClE,UAAI,wBAAwB,yBAAyB;AAGnD,mCAA2B,QAAQ,IAAI,UAAU,IAAI,mBAAmB;AACxE,cAAM,cAAc,eAAe,QAAQ,SAAS,MAAM,eAAe,SAAS;AAClF,cAAM,QAAQ,cAAc,sBAAsB,0BAA0B,0BAA0B;AACtG,cAAM,aAAa,oBAAoB;AAAA,UACrC;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,YAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,oBAAU,UAAU;AACpB,yBAAe,QAAQ,SAAS;AAChC,cAAII,WAAU;AACZ,YAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,cACzC;AAAA,cACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,YACvE,EAAE,CAAC;AAAA,UACL;AACA,6BAAmB,SAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,QACvG;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAGZ,QAAM,cAAc,YAAY,eAAa;AAC3C,UAAM;AAAA,MACJ,UAAAA;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,UAAU,YAAY,aAAa;AACrC,YAAM,wBAAwB,eAAe,IAAI,CAAAJ,eAAaA,WAAU,WAAW;AACnF,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAW,UAAU;AAClE,UAAI,wBAAwB,yBAAyB;AAEnD,cAAM,0BAA0B,2BAA2B,QAAQ,IAAI,UAAU,EAAE;AACnF,cAAM,qBAAqB,2BAA2B,QAAQ,2BAA2B,oBAAoB,0BAA0B;AACvI,cAAM,cAAc,eAAe,QAAQ,SAAS,MAAM,eAAe,SAAS;AAClF,cAAM,QAAQ,cAAc,sBAAsB,qBAAqB,qBAAqB;AAC5F,cAAM,aAAa,oBAAoB;AAAA,UACrC;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,YAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,oBAAU,UAAU;AACpB,yBAAe,QAAQ,SAAS;AAChC,cAAII,WAAU;AACZ,YAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,cACzC;AAAA,cACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,YACvE,EAAE,CAAC;AAAA,UACL;AACA,6BAAmB,SAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,QACvG;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAGZ,QAAM,eAAe,YAAY,eAAa;AAC5C,UAAM;AAAA,MACJ,QAAAD;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAWA,OAAM;AAC9D,WAAO;AAAA,MACL,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAGZ,QAAM,gBAAgB,YAAY,eAAa;AAC7C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,aAAa,eAAe,QAAQ,SAAS;AACnD,WAAO,yBAAyB;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,WAAW,MAAM,CAAC;AAGtB,QAAM,mBAAmB,YAAY,eAAa;AAChD,UAAM;AAAA,MACJ,QAAAA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAWA,OAAM;AAC9D,WAAO,gBAAgB,QAAQ,wBAAwB;AAAA,EACzD,GAAG,CAAC,OAAO,CAAC;AAGZ,QAAM,kBAAkB,YAAY,eAAa;AAC/C,UAAM;AAAA,MACJ,QAAAA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAWA,OAAM;AAC9D,WAAO,CAAC,eAAe,sBAAsB;AAAA,EAC/C,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,gBAAgB,YAAY,eAAa;AAC7C,UAAM;AAAA,MACJ,YAAAE;AAAA,MACA,IAAIH;AAAA,MACJ,UAAAE;AAAA,MACA,SAAAE;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,mBAAe,KAAK,SAAS;AAC7B,mBAAe,KAAK,CAAC,QAAQ,WAAW;AACtC,YAAM,SAAS,OAAO;AACtB,YAAM,SAAS,OAAO;AACtB,UAAI,UAAU,QAAQ,UAAU,MAAM;AACpC,eAAO;AAAA,MACT,WAAW,UAAU,MAAM;AACzB,eAAO;AAAA,MACT,WAAW,UAAU,MAAM;AACzB,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS;AAAA,MAClB;AAAA,IACF,CAAC;AAID,UAAM,gBAAgB,yBAAyBJ,QAAO;AACtD,QAAI,cAAc,WAAW,eAAe,QAAQ;AAClD;AAAA,IACF;AAIA,QAAI,eAAe;AACnB,QAAIG,aAAY;AACd,qBAAe,gBAAgBA,aAAY,gBAAgBC,QAAO;AAAA,IACpE;AACA,UAAM,kBAAkB,oCAAoCJ,QAAO;AACnE,QAAI,mBAAmB,GAAG;AACxB,UAAI,mCAAmC,eAAe,IAAI,CAAC;AAAA,QACzD;AAAA,MACF,MAAM,WAAW,CAAC,GAAG;AAEnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB,MAAM;AACxB,qBAAe,6BAA6B;AAAA,QAC1C;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAIA,UAAM,aAAa,yBAAyB;AAAA,MAC1C;AAAA,MACA,QAAQ;AAAA,MACR,kBAAkB,eAAe,IAAI,CAAAF,eAAaA,WAAU,WAAW;AAAA,IACzE,CAAC;AAKD,cAAU,UAAU;AACpB,mBAAe,QAAQ,SAAS;AAChC,QAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,UAAII,WAAU;AACZ,QAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,UACzC;AAAA,UACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,QACvE,EAAE,CAAC;AAAA,MACL;AACA,yBAAmBF,UAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,IACvG;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,uBAAuB,YAAY,kBAAgB;AACvD,WAAO,SAAS,cAAc,OAAO;AACnC,YAAM,eAAe;AACrB,YAAM;AAAA,QACJ,WAAAK;AAAA,QACA,WAAAC;AAAA,QACA,IAAIN;AAAA,QACJ,4BAAAO;AAAA,QACA,wBAAAC;AAAA,QACA,UAAAN;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI,eAAe;AACnB,YAAM;AAAA,QACJ;AAAA,MACF,IAAII,eAAc,QAAQA,eAAc,SAASA,aAAY,CAAC;AAC9D,YAAM,eAAe,sBAAsBN,UAAS,YAAY;AAChE,UAAI,QAAQ,yBAAyB,OAAOA,UAAS,cAAcK,YAAWC,YAAW;AAAA,QACvF,YAAYC;AAAA,QACZ,QAAQC;AAAA,MACV,CAAC;AACD,UAAI,UAAU,GAAG;AACf;AAAA,MACF;AAGA,YAAM,eAAeH,eAAc;AACnC,UAAI,SAAS,QAAQ,SAAS,cAAc;AAC1C,gBAAQ,CAAC;AAAA,MACX;AACA,YAAM,kBAAkB,oCAAoCL,QAAO;AACnE,YAAM,mBAAmB,eAAe,IAAI,eAAa,UAAU,WAAW;AAC9E,YAAM,aAAa,oBAAoB;AAAA,QACrC;AAAA,QACA;AAAA,QACA,QAAQ,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB;AAAA,QAC7E;AAAA,QACA;AAAA,QACA,SAAS,UAAU,KAAK,IAAI,aAAa;AAAA,MAC3C,CAAC;AACD,YAAM,gBAAgB,CAAC,eAAe,YAAY,UAAU;AAI5D,UAAI,aAAa,KAAK,KAAK,aAAa,KAAK,GAAG;AAI9C,YAAI,aAAa,WAAW,OAAO;AACjC,uBAAa,UAAU;AACvB,cAAI,CAAC,eAAe;AAKlB,gBAAI,cAAc;AAChB,mCAAqB,QAAQ,IAAI,mBAAmB,gBAAgB;AAAA,YACtE,OAAO;AACL,mCAAqB,QAAQ,IAAI,iBAAiB,cAAc;AAAA,YAClE;AAAA,UACF,OAAO;AAEL,iCAAqB,eAAe,eAAe,UAAU;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AACA,UAAI,eAAe;AACjB,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAIE,WAAU;AACZ,UAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,YACzC;AAAA,YACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,UACvE,EAAE,CAAC;AAAA,QACL;AACA,2BAAmBF,UAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,MACvG;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,QAAMZ,eAAc,YAAY,CAAC,WAAW,eAAe;AACzD,UAAM;AAAA,MACJ,UAAAc;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,wBAAwB,eAAe,IAAI,CAAAJ,eAAaA,WAAU,WAAW;AACnF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,SAAS,gBAAgB,WAAW,UAAU;AAClE,UAAM,iBAAiB,gCAAgC,YAAY,eAAe;AAClF,UAAM,cAAc,eAAe,QAAQ,SAAS,MAAM,eAAe,SAAS;AAClF,UAAM,QAAQ,cAAc,sBAAsB,iBAAiB,iBAAiB;AACpF,UAAM,aAAa,oBAAoB;AAAA,MACrC;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,gBAAU,UAAU;AACpB,qBAAe,QAAQ,SAAS;AAChC,UAAII,WAAU;AACZ,QAAAA,UAAS,WAAW,IAAI,CAAAO,qBAAmB;AAAA,UACzC,gBAAAA;AAAA,UACA,YAAY,0BAA0BA,iBAAgB,eAAe;AAAA,QACvE,EAAE,CAAC;AAAA,MACL;AACA,yBAAmB,SAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,IACvG;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,gBAAgB,YAAY,CAAC,cAAc,UAAU;AACzD,UAAM;AAAA,MACJ,WAAAJ;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAAJ;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,gBAAgB,uBAAuB,YAAY;AACzD,UAAM,wBAAwB,6BAA6BI,YAAW,KAAK;AAC3E,iBAAa;AAAA,MACX;AAAA,MACA,gBAAgB,cAAc,sBAAsB;AAAA,MACpD;AAAA,MACA,eAAeJ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,QAAM,eAAe,YAAY,MAAM;AACrC,2BAAuB;AACvB,iBAAa,IAAI;AAAA,EACnB,GAAG,CAAC,CAAC;AACL,QAAM,qBAAqB,OAAO;AAAA,IAChC,iBAAiB,oBAAI,IAAI;AAAA,IACzB,SAAS;AAAA,EACX,CAAC;AACD,QAAM,kBAAkB,YAAY,eAAa;AAC/C,UAAM;AAAA,MACJ,IAAID;AAAA,MACJ,UAAAE;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,QAAQ,eAAe,QAAQ,SAAS;AAC9C,QAAI,SAAS,GAAG;AACd,qBAAe,OAAO,OAAO,CAAC;AAC9B,yBAAmB,QAAQ,gBAAgB,IAAI,UAAU,EAAE;AAAA,IAC7D;AACA,QAAI,mBAAmB,QAAQ,WAAW,MAAM;AAC9C,mBAAa,mBAAmB,QAAQ,OAAO;AAAA,IACjD;AAKA,uBAAmB,QAAQ,UAAU,WAAW,MAAM;AACpD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM,MAAM,sCAAsC;AAIlD,UAAI,yBAAyB;AAC7B,sBAAgB,QAAQ,aAAW;AACjC,wBAAgB,OAAO,OAAO;AAC9B,YAAI,eAAe,KAAK,CAAC;AAAA,UACvB;AAAA,QACF,MAAM,OAAO,OAAO,KAAK,MAAM;AAC7B,mCAAyB;AAMzB,iBAAO,IAAI,UAAU,EAAE;AAAA,QACzB;AAAA,MACF,CAAC;AACD,UAAI,CAAC,wBAAwB;AAC3B;AAAA,MACF;AACA,UAAI,eAAe,WAAW,GAAG;AAE/B;AAAA,MACF;AACA,YAAM,kBAAkB,oCAAoCF,QAAO;AACnE,UAAI,eAAe,6BAA6B;AAAA,QAC9C;AAAA,QACA;AAAA,MACF,CAAC;AAID,YAAM,aAAa,yBAAyB;AAAA,QAC1C;AAAA,QACA,QAAQ;AAAA,QACR,kBAAkB,eAAe,IAAI,CAAAF,eAAaA,WAAU,WAAW;AAAA,MACzE,CAAC;AACD,UAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAII,WAAU;AACZ,UAAAA,UAAS,WAAW,IAAI,qBAAmB;AAAA,YACzC;AAAA,YACA,YAAY,0BAA0B,gBAAgB,eAAe;AAAA,UACvE,EAAE,CAAC;AAAA,QACL;AACA,2BAAmBF,UAAS,gBAAgB,YAAY,sCAAsC,OAAO;AAAA,MACvG;AAAA,IACF,GAAG,CAAC;AAAA,EACN,GAAG,CAAC,CAAC;AACL,QAAM,UAAU,QAAQ,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,eAAe,WAAW,WAAW,aAAa,cAAc,eAAe,SAAS,kBAAkB,iBAAiB,eAAe,sBAAsBA,cAAa,eAAe,cAAc,eAAe,CAAC;AAC/N,QAAM,QAAQ;AAAA,IACZ,SAAS;AAAA,IACT,eAAe,cAAc,eAAe,QAAQ;AAAA,IACpD,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACA,SAAO,cAAc,kBAAkB,UAAU;AAAA,IAC/C,OAAO;AAAA,EACT,GAAG,cAAc,MAAM;AAAA,IACrB;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA;AAAA,IAEH,oBAAoB;AAAA,IACpB,8BAA8B;AAAA,IAC9B,uBAAuB;AAAA,EACzB,CAAC,CAAC;AACJ;AACA,IAAM,aAAa,WAAW,CAAC,OAAO,QAAQ,cAAc,4BAA4B;AAAA,EACtF,GAAG;AAAA,EACH,cAAc;AAChB,CAAC,CAAC;AACF,2BAA2B,cAAc;AACzC,WAAW,cAAc;AACzB,SAAS,gBAAgB,SAAS,gBAAgB,WAAW,QAAQ;AACnE,QAAM,wBAAwB,eAAe,IAAI,CAAAU,eAAaA,WAAU,WAAW;AACnF,QAAM,aAAa,eAAe,QAAQ,SAAS;AACnD,QAAM,mBAAmB,sBAAsB,UAAU;AACzD,QAAM,kBAAkB,oCAAoC,OAAO;AACnE,QAAM,6BAA6B,kCAAkC,uBAAuB,YAAY,eAAe;AACvH,QAAM,cAAc,eAAe,eAAe,SAAS;AAC3D,QAAM,eAAe,cAAc,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,YAAY,aAAa,CAAC;AAC7F,QAAM,sBAAsB,OAAO,UAAU;AAC7C,QAAM,kBAAkB,0BAA0B,qBAAqB,eAAe;AACtF,SAAO;AAAA,IACL,GAAG;AAAA,IACH,aAAa,iBAAiB;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAIA,SAAS,uCAAuC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,YAAU,MAAM;AACd,QAAI,YAAY,iBAAiB,MAAM;AACrC;AAAA,IACF;AACA,UAAM,gBAAgB,uBAAuB,QAAQ;AACrD,QAAI,iBAAiB,MAAM;AACzB;AAAA,IACF;AACA,UAAM,YAAY,WAAS;AACzB,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,cAAQ,MAAM,KAAK;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QACH;AACE,gBAAM,eAAe;AACrB,wBAAc,KAAK;AACnB;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,gBAAM,eAAe;AACrB,gBAAM,UAAU,cAAc,aAAa,qBAAqB;AAChE,gBAAM,UAAU,gCAAgC,OAAO;AACvD,gBAAM,QAAQ,4BAA4B,SAAS,QAAQ;AAC3D,iBAAO,UAAU,IAAI;AACrB,gBAAM,YAAY,MAAM,WAAW,QAAQ,IAAI,QAAQ,IAAI,QAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,SAAS,QAAQ,IAAI;AACzH,gBAAM,aAAa,QAAQ,SAAS;AACpC,qBAAW,MAAM;AACjB;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,kBAAc,iBAAiB,WAAW,SAAS;AACnD,WAAO,MAAM;AACX,oBAAc,oBAAoB,WAAW,SAAS;AAAA,IACxD;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,aAAa,CAAC;AACxC;AAEA,SAAS,kBAAkB;AAAA,EACzB,WAAW;AAAA,EACX,WAAW,qBAAqB;AAAA,EAChC;AAAA,EACA,WAAW;AAAA,EACX,IAAI,cAAc;AAAA,EAClB;AAAA,EACA,OAAO,iBAAiB,CAAC;AAAA,EACzB,SAAS,OAAO;AAClB,GAAG;AACD,QAAM,gBAAgB,OAAO,IAAI;AAGjC,QAAM,eAAe,OAAO;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,YAAU,MAAM;AACd,iBAAa,QAAQ,aAAa;AAAA,EACpC,CAAC;AACD,QAAM,oBAAoB,WAAW,iBAAiB;AACtD,MAAI,sBAAsB,MAAM;AAC9B,UAAM,MAAM,6EAA6E;AAAA,EAC3F;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,YAAY,WAAW;AAC9C,QAAM,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB;AACtG,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,KAAK;AAChD,QAAM,CAAC,eAAe,gBAAgB,IAAI,SAAS,IAAI;AACvD,QAAM,sBAAsB,YAAY,MAAM;AAG5C,UAAM,MAAM,cAAc;AAC1B,QAAI,KAAK;AACT,iBAAa;AACb,UAAM;AAAA,MACJ,YAAAY;AAAA,IACF,IAAI,aAAa;AACjB,QAAIA,aAAY;AACd,MAAAA,YAAW,KAAK;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,YAAU,MAAM;AACd,QAAI,UAAU;AACZ,uBAAiB,IAAI;AAAA,IACvB,OAAO;AACL,YAAMC,iBAAgB,qBAAqB,cAAc;AACzD,uBAAiB,MAAMA,cAAa;AAAA,IACtC;AAAA,EACF,GAAG,CAAC,UAAU,gBAAgB,oBAAoB,CAAC;AACnD,YAAU,MAAM;AACd,QAAI,YAAY,iBAAiB,QAAQ,CAAC,YAAY;AACpD;AAAA,IACF;AACA,UAAM,SAAS,WAAS;AACtB,oBAAc,KAAK;AAAA,IACrB;AACA,UAAM,eAAe,WAAS;AAC5B,oBAAc,KAAK;AAAA,IACrB;AACA,UAAM,aAAa,cAAc;AACjC,UAAM,iBAAiB,WAAW;AAClC,mBAAe,KAAK,iBAAiB,eAAe,mBAAmB;AACvE,mBAAe,KAAK,iBAAiB,aAAa,MAAM;AACxD,mBAAe,KAAK,iBAAiB,aAAa,MAAM;AACxD,mBAAe,KAAK,iBAAiB,cAAc,YAAY;AAC/D,WAAO,iBAAiB,WAAW,mBAAmB;AACtD,WAAO,iBAAiB,YAAY,mBAAmB;AACvD,WAAO,MAAM;AACX,qBAAe,KAAK,oBAAoB,eAAe,mBAAmB;AAC1E,qBAAe,KAAK,oBAAoB,aAAa,MAAM;AAC3D,qBAAe,KAAK,oBAAoB,aAAa,MAAM;AAC3D,qBAAe,KAAK,oBAAoB,cAAc,YAAY;AAClE,aAAO,oBAAoB,WAAW,mBAAmB;AACzD,aAAO,oBAAoB,YAAY,mBAAmB;AAAA,IAC5D;AAAA,EACF,GAAG,CAAC,WAAW,UAAU,YAAY,eAAe,mBAAmB,CAAC;AACxE,yCAAuC;AAAA,IACrC;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,QAAQ,eAAe,SAAS;AAAA,IAChC,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AACA,SAAO,cAAc,MAAM;AAAA,IACzB;AAAA,IACA,WAAW;AAAA,IACX,QAAQ,MAAM,aAAa,KAAK;AAAA,IAChC,SAAS,MAAM,aAAa,IAAI;AAAA,IAChC,aAAa,WAAS;AACpB,oBAAc,gBAAgB,MAAM,WAAW;AAC/C,YAAM;AAAA,QACJ,YAAAD;AAAA,MACF,IAAI,aAAa;AACjB,UAAIA,aAAY;AACd,QAAAA,YAAW,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,IACA,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,cAAc,WAAS;AACrB,oBAAc,gBAAgB,MAAM,WAAW;AAC/C,YAAM;AAAA,QACJ,YAAAA;AAAA,MACF,IAAI,aAAa;AACjB,UAAIA,aAAY;AACd,QAAAA,YAAW,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA;AAAA,IAEH,8BAA8B;AAAA,IAC9B,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,6BAA6B,aAAa,YAAY,YAAY,aAAa;AAAA,IAC/E,oCAAoC,CAAC;AAAA,IACrC,+BAA+B;AAAA,EACjC,CAAC;AACH;AACA,kBAAkB,cAAc;", "names": ["resizePanel", "collapsedSizePercentage", "maxSizePercentage", "minSizePercentage", "delta", "pivotIndex", "unsafeSize", "safeSize", "element", "panelDataArray", "panelData", "constraints", "groupId", "layout", "onLayout", "autoSaveId", "storage", "direction", "dragState", "keyboardResizeByPercentage", "keyboardResizeByPixels", "sizePercentage", "onDragging", "resize<PERSON><PERSON>ler"]}